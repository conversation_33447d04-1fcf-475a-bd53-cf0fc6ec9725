import React, {lazy, ReactNode, useContext, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useLocation, useNavigate, useSearchParams} from 'react-router-dom';
import {ProColumns, ProFormInstance, ProTable} from '@ant-design/pro-components';
import ModelUsagePie from '../Chart/ModelUsagePie';
import {ConsumptionMetricsPanel} from '../Chart/Metrics';

import {
    AutoComplete,
    Button,
    Card,
    Col,
    Collapse,
    Empty,
    message as AntdMessage,
    Modal,
    Popconfirm,
    Popover,
    Row,
    Select,
    Space,
    Spin,
    Switch,
    Tag,
    Tooltip,
    Typography
} from 'antd';
import {
    API,
    getRecentChartData,
    hasPermission,
    isAdmin,
    isMobile,
    Permission,
    showError,
    timestamp2string
} from '../../helpers';
import {createRenderFunctions, renderModelTags,} from '../../helpers/render';
import moment from 'moment';
import ReactECharts from 'echarts-for-react';
import {
    ClockCircleFilled,
    ControlFilled,
    FileExcelFilled,
    IdcardFilled,
    InfoCircleOutlined,
    LoadingOutlined,
    LockFilled,
    // 添加不同请求类型的图标
    MessageOutlined,
    PictureOutlined,
    SoundOutlined,
    SearchOutlined,
    CheckCircleOutlined,
    ThunderboltOutlined,
    ApiOutlined,
    GlobalOutlined,
    ExperimentOutlined,
    SettingOutlined
} from '@ant-design/icons';
import Papa from 'papaparse';
import {
    AUTOCOMPLETE_ERROR_CODES,
    AUTOCOMPLETE_MODEL_NAMES,
    LOGS_TYPE,
    LOGS_TYPE_OPTIONS,
    paginationProps
} from "../../constants";
import {UserContext} from "../../context/User";
import {StatusContext} from "../../context/Status";
import {useGroup} from '../../context/Group';
import DailyUsageByModelEcharts from "../Chart/DailyUsageModelEcharts";
import {useActionRef} from "../../hooks/useActionRef";
import {isInternalIP} from '../../utils/ipUtils';
import LogContentDetail from "./LogContentDetail";
import {getNumberDecimalPlaces} from '../../utils/numberUtils';

const {Meta} = Card;
const {Text} = Typography;
const LogDetailDrawer = lazy(() => import('./LogDetailDrawer'));

// 请求类型配置
const REQUEST_TYPE_CONFIG = {
    text: {
        icon: MessageOutlined,
        color: '#1890ff',
        label: '文本',
        description: '文本生成和对话请求'
    },
    image: {
        icon: PictureOutlined,
        color: '#52c41a',
        label: '图片',
        description: '图片生成和编辑请求'
    },
    audio: {
        icon: SoundOutlined,
        color: '#fa8c16',
        label: '音频',
        description: '语音识别、转换和生成'
    },
    embedding: {
        icon: SearchOutlined,
        color: '#722ed1',
        label: '嵌入',
        description: '文本向量化和嵌入'
    },
    moderation: {
        icon: CheckCircleOutlined,
        color: '#f5222d',
        label: '审核',
        description: '内容安全检测'
    },
    realtime: {
        icon: ThunderboltOutlined,
        color: '#fa541c',
        label: '实时',
        description: '实时对话和通信'
    },
    midjourney: {
        icon: ExperimentOutlined,
        color: '#eb2f96',
        label: 'MJ',
        description: 'Midjourney 图片生成'
    },
    search: {
        icon: GlobalOutlined,
        color: '#13c2c2',
        label: '搜索',
        description: '搜索和检索服务'
    },
    fish_audio: {
        icon: SoundOutlined,
        color: '#2f54eb',
        label: 'Fish',
        description: 'Fish 音频处理服务'
    },
    proxy: {
        icon: ApiOutlined,
        color: '#8c8c8c',
        label: '代理',
        description: '代理转发请求'
    },
    dynamic_route: {
        icon: GlobalOutlined,
        color: '#1890ff',
        label: '动态路由',
        description: '动态路由服务，支持个性化折扣'
    },
    default: {
        icon: SettingOutlined,
        color: '#595959',
        label: '其他',
        description: '其他类型请求'
    }
};

interface DailyModelUsageStat {
    sumQuota: number;

    [key: string]: any;
}

// 添加接口定义
interface GroupOption {
    id: number;
    name: string;
    display_name: string;
    convert_ratio: number;
    current_group_ratio: number;
    current_topup_ratio: number;
}

// 添加类型定义
interface LogParams {
    current?: number;
    pageSize?: number;

    [key: string]: any;
}

// 添加 stat 类型定义
interface Stat {
    quota: number;
    token: number;
    rpm: number;
    tpm: number;
    mpm: number;
    is_realtime_data?: boolean;  // 添加新字段
}

// 添加类型定义
interface RequestLog {
    id: number;
    created_at: number;
    model_name: string;
    token_name: string;
    quota: number;
    error_code?: string;
    content: string;
}

// 组件接口定义
interface LogsTableProps {
    useResizableTable?: boolean;
    setUseResizableTable?: (value: boolean) => void;
}

const MemoizedPie = React.memo(({data}: { data: any[] }) => {
    return <ModelUsagePie data={data}/>;
}, (prevProps, nextProps) => {
    return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
});

const LogsTable: React.FC<LogsTableProps> = ({ 
    useResizableTable, 
    setUseResizableTable 
}) => {
    const {t} = useTranslation();
    const {
        renderModels,
        renderQuota,
        renderRoleTag,
        renderQuotaExpireTime,
        renderChannelStatusTag,
        renderIsStream,
        renderRequestDuration,
        renderResponseFirstByteDuration,
        renderTotalDuration
    } = createRenderFunctions(t);
    const [logs, setLogs] = useState([]);
    const [userState] = useContext(UserContext);
    const [statusState] = useContext(StatusContext);
    const {groups, loading: groupsLoading, fetchGroups} = useGroup();
    const location = useLocation();
    const [lastParams, setLastParams] = useState<LogParams | null>(null);
    const [modelUsageData, setModelUsageData] = useState([]);
    const [dynamicModelNames, setDynamicModelNames] = useState<string[]>([]); // 新增：动态模型名称列表

    // 合并动态模型名称和静态模型名称，去重并排序
    const combinedModelNames = useMemo(() => {
        const allModels = [...dynamicModelNames, ...AUTOCOMPLETE_MODEL_NAMES];
        const uniqueModels = Array.from(new Set(allModels)).filter(Boolean);
        // 将动态获取的模型名称排在前面，然后是静态的
        const dynamicSet = new Set(dynamicModelNames);
        const dynamicFirst = uniqueModels.filter(model => dynamicSet.has(model));
        const staticRest = uniqueModels.filter(model => !dynamicSet.has(model));
        return [...dynamicFirst, ...staticRest];
    }, [dynamicModelNames]);

    // 处理统计图模型列表变化的回调
    const handleModelsChange = useCallback((models: string[]) => {
        setDynamicModelNames(models);
    }, []);
    const [loadingModelUsageData, setLoadingModelUsageData] = useState(true);
    const [logsCount, setLogsCount] = useState(0);
    const [stat, setStat] = useState<Stat>({quota: 0, token: 0, rpm: 0, tpm: 0, mpm: 0});
    const [dailyModelUsageStats, setDailyModelUsageStats] = useState([] as DailyModelUsageStat[]);
    const [loadingDailyModelUsageStats, setLoadingDailyModelUsageStats] = useState(true);
    const [customShowDailyModelUsageStats, setCustomShowDailyModelUsageStats] = useState(localStorage.getItem('customShowDailyModelUsageStats') === 'true');
    const [needGetSata, setNeedGetSata] = useState(statusState.customConfig.CustomAutoGetLogStat);
    const [isSortByIds, setIsSortByIds] = useState(false);
    const [unbindOnly, setUnbindOnly] = useState(false);
    const [durationType, setDurationType] = useState("2");
    const [searchParams, setSearchParams] = useSearchParams();
    // 使用context中的groups数据，不再需要本地state
    const [excludedModels, setExcludedModels] = useState<string[]>([]);
    // 新增：是否默认只显示当天的配置（默认为 false）
    const defaultTimeTodayOnly = statusState.customConfig.CustomDefaultTimeTodayOnly || false;
    // 新增：默认的使用情况统计时间单位（默认为 'day'）
    const usageStatsDefaultTimeUnit = statusState.customConfig.CustomUsageStatsDefaultTimeUnit || 'day';
    // 用户自定义配置状态
    const [userOnlyToday, setUserOnlyToday] = useState(localStorage.getItem('userOnlyToday') === 'true');
    const [userTimeUnit, setUserTimeUnit] = useState(localStorage.getItem('userTimeUnit') || 'day');

    const formRef = useRef<ProFormInstance>();

    const {ref, safeAction} = useActionRef();

    // 添加一个新的状态来跟踪统计数据是否正在加载
    const [loadingStats, setLoadingStats] = useState(false);

    // 添加 loading 状态
    const [loading, setLoading] = useState(true);

    // 添加一个新的状态来跟踪查看总额的加载状态
    const [loadingTotalQuota, setLoadingTotalQuota] = useState(false);

    const navigate = useNavigate();

    // 添加新的状态
    const [isRequestLogsModalVisible, setIsRequestLogsModalVisible] = useState(false);
    const [currentRequestId, setCurrentRequestId] = useState('');
    const [requestLogs, setRequestLogs] = useState<RequestLog[]>([]);
    const [loadingRequestLogs, setLoadingRequestLogs] = useState(false);

    // 添加自动刷新相关的状态
    const [autoRefreshInterval, setAutoRefreshInterval] = useState(0);
    const [refreshTimer, setRefreshTimer] = useState<NodeJS.Timeout | null>(null);

    // 添加列状态映射
    const [columnsStateMap, setColumnsStateMap] = useState(() => {
        try {
            const saved = localStorage.getItem('logsTableColumnsConfig');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            console.error('解析保存的列配置失败:', e);
            return {};
        }
    });

    // 保存列配置的处理函数
    const handleColumnsStateChange = (map) => {
        setColumnsStateMap(map);
        try {
            localStorage.setItem('logsTableColumnsConfig', JSON.stringify(map));
        } catch (e) {
            console.error('保存列配置失败:', e);
        }
    };

    useEffect(() => {
        fetchGroups();
        const params = {};
        for (let [key, value] of searchParams.entries()) {
            params[key] = value;
        }
        setSearchParams(params);
    }, [searchParams]);

    const forceResetFields = () => {
        const form = formRef.current?.getFieldsFormatValue?.();
        if (form) {
            Object.keys(form).forEach(key => {
                formRef.current?.setFieldsValue({
                    [key]: undefined
                });
            });
        }
    };

    // 使用context获取分组数据
    useEffect(() => {
        fetchGroups();
    }, []);

    // 监听用户配置变化并更新
    useEffect(() => {
        localStorage.setItem('userOnlyToday', userOnlyToday ? 'true' : 'false');
    }, [userOnlyToday]);
    
    useEffect(() => {
        localStorage.setItem('userTimeUnit', userTimeUnit);
    }, [userTimeUnit]);
    
    // 使用用户配置或默认配置
    const finalDefaultTimeTodayOnly = userOnlyToday !== null ? userOnlyToday : defaultTimeTodayOnly;
    const finalUsageStatsDefaultTimeUnit = userTimeUnit || usageStatsDefaultTimeUnit;
    
    // 修改获取时间戳的逻辑，使用最终配置
    const query = new URLSearchParams(location.search);
    const customLogQueryTime = statusState.customConfig.CustomLogQueryDuration;
    const start_timestamp = Number(query.get('start_timestamp')) || 
        (finalDefaultTimeTodayOnly ? moment().startOf('day').unix() :  
        (customLogQueryTime !== 0 ? moment().subtract(customLogQueryTime - 1, 'days').startOf('day').unix() : 0));
    const end_timestamp = Number(query.get('end_timestamp')) || 
        (finalDefaultTimeTodayOnly ? moment().endOf('day').unix() :  
        (customLogQueryTime !== 0 ? moment().endOf('day').unix() : 0));
    const startTime = start_timestamp !== 0 ? moment.unix(start_timestamp) : undefined;
    const endTime = end_timestamp !== 0 ? moment.unix(end_timestamp) : undefined;

    interface LogPromiseResult {
        data: { success: boolean; data: any; };
    }

    const request = async (params: { [x: string]: any; current?: any; pageSize?: any; }) => {
        const {current, pageSize, ...rest} = params;
        let query = '';
        Object.keys(rest).forEach(key => {
            query += `&${key}=${rest[key]}`;
        });
        let url: string;
        if (isAdmin(userState)) {
            url = `/api/log/?p=${current - 1}&pageSize=${pageSize}${query}`;
        } else {
            url = `/api/log/self/?p=${current - 1}&pageSize=${pageSize}${query}`;
        }
        const logPromise: Promise<LogPromiseResult> = API.get(url);
        let fetchStatsDataPromise: Promise<any> = Promise.resolve();
        let logCountPromise: Promise<any> = Promise.resolve();
        let logStatPromise: Promise<any> = Promise.resolve();
        if (!lastParams || JSON.stringify(params) !== JSON.stringify(lastParams)) {
            fetchStatsDataPromise = fetchStatsData(params as any, undefined);
            logCountPromise = getLogCount(params);
            logStatPromise = isAdmin(userState) ? getLogStat(params) : getLogSelfStat(params);
        }
        try {
            const logPromiseResult: LogPromiseResult = await logPromise;
            const {success, data} = logPromiseResult.data;
            if (success) {
                setLastParams(params as any);
                setLogs(data);
                const result = {success: true, data: data, total: undefined};
                (async () => {
                    try {
                        await Promise.all([fetchStatsDataPromise, logCountPromise, logStatPromise]);
                    } catch (error) {
                        showError(error as any);
                    }
                })();
                return result;
            }
        } catch (error) {
            setLogs([]);
            showError(error as any);
        }
    };

    const [showLogDetailDrawerLogId, setShowLogDetailDrawerLogId] = useState(0);
    const [isLogDetailDrawerVisible, setIsLogDetailDrawerVisible] = useState(false);
    // 在组件内部添加状态
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [currentContent, setCurrentContent] = useState('');

    const handelOpenLogDetailDrawer = (logId: number) => {
        if (!logId || logId === 0) {
            AntdMessage.error(t('logsTable.logIdNotExist'));
            return;
        }
        setShowLogDetailDrawerLogId(logId);
        setIsLogDetailDrawerVisible(true);
    }

    const getLogSelfStat = async (params: any, force = false) => {
        if (!needGetSata && !force) return;
        setLoadingStats(true);
        try {
            const {current, pageSize, ...rest} = params;
            
            // 优化时间参数传递，确保使用数字格式
            let queryParams = {...rest};
            if (start_timestamp && !isNaN(Number(start_timestamp))) {
                queryParams.start_timestamp = Number(start_timestamp);
            }
            if (end_timestamp && !isNaN(Number(end_timestamp))) {
                queryParams.end_timestamp = Number(end_timestamp);
            }
            
            // 构建查询字符串
            let query = '';
            Object.keys(queryParams).forEach(key => {
                if (queryParams[key] !== undefined && queryParams[key] !== null) {
                    query += `&${key}=${queryParams[key]}`;
                }
            });
            
            const res = await API.get(`/api/log/self/stat?${query}`);
            const {success, data} = res.data;
            if (success) setStat(data);
        } finally {
            setLoadingStats(false);
        }
    };

    const getLogStat = async (params: any, force = false) => {
        if (!needGetSata && !force) return;
        setLoadingStats(true);
        try {
            const {current, pageSize, ...rest} = params;
            
            // 优化时间参数传递，确保使用数字格式
            let queryParams = {...rest};
            if (start_timestamp && !isNaN(Number(start_timestamp))) {
                queryParams.start_timestamp = Number(start_timestamp);
            }
            if (end_timestamp && !isNaN(Number(end_timestamp))) {
                queryParams.end_timestamp = Number(end_timestamp);
            }
            
            // 构建查询字符串
            let query = '';
            Object.keys(queryParams).forEach(key => {
                if (queryParams[key] !== undefined && queryParams[key] !== null) {
                    query += `&${key}=${queryParams[key]}`;
                }
            });
            
            const res = await API.get(`/api/log/stat?${query}`);
            const {success, data} = res.data;
            if (success) setStat(data);
        } finally {
            setLoadingStats(false);
        }
    }

    const getModelUsage = async (params: { current?: string, pageSize?: number } = {}) => {
        const {current, pageSize, ...rest} = params;
        let query = '';
        Object.keys(rest).forEach(key => {
            query += `&${key}=${rest[key]}`;
        });
        const useNewDataApi = statusState.status.DataExportEnabled &&
            statusState.status.DataExportDisplayEnabled;
        let url;
        if (useNewDataApi) {
            url = `/api/data/model_stats?${query}`;
        } else {
            url = isAdmin(userState)
                ? `/api/log/model_usage?${query}`
                : `/api/log/self/model_usage?${query}`;
        }
        const res = await API.get(url);
        const {success, message, data} = res.data;
        if (success) {
            return data;
        } else {
            showError(message);
        }
    };

    const getLogCount = async (params: { [x: string]: any; current?: any; pageSize?: any; }) => {
        const {current, pageSize, ...rest} = params;
        let query = '';
        Object.keys(rest).forEach(key => {
            query += `&${key}=${rest[key]}`;
        });
        let url: string;
        if (isAdmin(userState)) {
            url = `/api/log/count/?${query}`;
        } else {
            url = `/api/log/self/count?${query}`;
        }
        let res = await API.get(url);
        const {success, message, data} = res.data;
        if (success) {
            setLogsCount(data.count);
        } else {
            showError(message);
        }
    };

    const downloadCSV = (event) => {
        event.stopPropagation();
        if (!logs.length) {
            AntdMessage.info(t('logsTable.noDataToExport'));
            return;
        }
        const filteredData = logs.map((row: any) => {
            let rowObj = {
                [t('logsTable.usageTime')]: timestamp2string(row.created_at),
                [t('logsTable.tokenName')]: row.token_name,
                [t('logsTable.modelName')]: row.model_name,
                [t('logsTable.prompt')]: row.prompt_tokens,
                [t('logsTable.completion')]: row.completion_tokens,
                [t('logsTable.quotaConsumption')]: `$${(row.quota / 500000).toFixed(6)}`,
                [t('logsTable.timeConsumption')]: `${row.request_duration} ${t('logsTable.seconds')}`
            };
            if (isAdmin(userState)) {
                rowObj[t('logsTable.user')] = row.username;
                rowObj[t('logsTable.channel')] = row.channel;
                rowObj[t('logsTable.channelName')] = row.channel_name;
                rowObj[t('logsTable.description')] = row.content;
            }
            return rowObj;
        });

        const csv = Papa.unparse(filteredData);
        const blob = new Blob([csv], {type: 'text/csv'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'logs.csv';
        a.click();
        AntdMessage.success(t('logsTable.exportSuccess'));
    };

    const fetchStatsData = async (params: {} | null | undefined, reload: boolean | undefined) => {
        setLoadingDailyModelUsageStats(true);
        setLoadingModelUsageData(true);
        if (!customShowDailyModelUsageStats && !reload) {
            return Promise.resolve();
        }
        try {
            // 只获取 modelUsageData，移除 getDailyUsageStatsByDimension 调用
            const modelUsageData = await getModelUsage(params ?? {});
            setModelUsageData(modelUsageData?.map(item => ({type: item.modelName, value: item.cnt})));
            setLoadingModelUsageData(false);
        } finally {
            setLoadingDailyModelUsageStats(false);
        }
    };

    // 渲染请求类型图标和标签
    const renderRequestType = (requestType: string, record?: any) => {
        // 如果日志内容包含"路由折扣率"，则识别为动态路由
        if (record && record.content && record.content.includes('路由折扣率')) {
            requestType = 'dynamic_route';
        }
        
        const config = REQUEST_TYPE_CONFIG[requestType] || REQUEST_TYPE_CONFIG.default;
        const IconComponent = config.icon;
        
        return (
            <Tooltip title={config.description}>
                <Tag 
                    icon={<IconComponent />} 
                    color={config.color}
                    style={{
                        borderRadius: '3px',
                        fontSize: '11px',
                        fontWeight: 400,
                        lineHeight: '16px',
                        padding: '0 4px',
                        margin: '0 2px'
                    }}
                >
                    {config.label}
                </Tag>
            </Tooltip>
        );
    };

    // 增强的描述渲染函数，根据请求类型显示不同的计费模板
    const renderEnhancedDescription = (record: any, isAdmin: boolean = false) => {
        let otherObj;
        try {
            otherObj = record.other ? JSON.parse(record.other) : null;
        } catch (e) {
            console.error('Failed to parse other field:', e);
        }

        // 获取请求类型和计费类型
        const requestType = otherObj?.request_type || 'default';
        const billingType = otherObj?.billing_type || 'by_quota';
        const typeConfig = REQUEST_TYPE_CONFIG[requestType] || REQUEST_TYPE_CONFIG.default;

        // 如果有错误码，显示带错误码的 Tooltip（仅管理员）
        if (isAdmin && record.error_code) {
            return (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {renderRequestType(requestType, record)}
                    <Tooltip
                        title={
                            <div style={{whiteSpace: 'pre-line'}}>
                                {`${record.content}\n${t('logsTable.errorCode')}: ${record.error_code}`}
                            </div>
                        }
                        mouseEnterDelay={0}
                        placement="left"
                    >
                        <span
                            style={{cursor: 'pointer', color: '#ff4d4f', flex: 1}}
                            onClick={(e) => {
                                e.stopPropagation();
                                setCurrentContent(record.content);
                                setIsModalVisible(true);
                            }}
                        >
                            {record.content}
                        </span>
                    </Tooltip>
                </div>
            );
        }

        // 渲染结构化的计费明细（使用 billing_detail 字段）
        const renderStructuredBillingDetails = (billingDetail) => {
            const {
                billing_type,
                route,
                field_details = [],
                group_ratio = 1,
                topup_convert_ratio = 1,
                user_discount = 1,
                route_discount = 1,
                base_quota = 0,
                final_quota = 0
            } = billingDetail;

            return (
                <div className="billing-details" style={{ fontSize: '14px', lineHeight: '1.6' }}>
                    <div className="billing-section" style={{ marginBottom: '16px' }}>
                        <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>
                            🌐 动态路由计费详情
                        </div>
                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                            <span style={{ fontWeight: '500' }}>路由路径:</span>
                            <span style={{ fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: '2px 4px', borderRadius: '3px' }}>
                                {route}
                            </span>
                        </div>
                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                            <span style={{ fontWeight: '500' }}>计费类型:</span>
                            <Tag color={billing_type === 'dynamic_fields' ? 'blue' : billing_type === 'fixed' ? 'green' : 'orange'}>
                                {billing_type === 'dynamic_fields' ? '动态字段计费' :
                                 billing_type === 'fixed' ? '固定计费' :
                                 billing_type === 'sub_price_match' ? '价格匹配计费' : billing_type}
                            </Tag>
                        </div>
                    </div>

                    {/* 字段计费明细 */}
                    {field_details && field_details.length > 0 && (
                        <div className="billing-section" style={{ marginBottom: '16px' }}>
                            <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
                                📊 字段计费明细
                            </div>
                            {field_details.map((detail, index) => (
                                <div key={index} style={{
                                    backgroundColor: '#f9f9f9',
                                    padding: '8px',
                                    marginBottom: '8px',
                                    borderRadius: '4px',
                                    border: '1px solid #e8e8e8'
                                }}>
                                    <div style={{ fontWeight: '500', marginBottom: '4px' }}>
                                        字段: <code style={{ backgroundColor: '#fff', padding: '2px 4px', borderRadius: '2px' }}>{detail.field}</code>
                                    </div>
                                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                                        {detail.description || '无描述'}
                                    </div>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                                        <span>值: {detail.value}</span>
                                        <span>倍率: {detail.multiplier}</span>
                                        <span>基数: {detail.base_unit}</span>
                                        <span style={{ fontWeight: 'bold' }}>费用: ${(detail.field_quota / 500000).toFixed(6)}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* 倍率计算详情 */}
                    <div className="billing-section" style={{ marginBottom: '16px' }}>
                        <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#fa8c16' }}>
                            🧮 倍率计算
                        </div>
                        <div style={{ backgroundColor: '#fff7e6', padding: '12px', borderRadius: '4px', border: '1px solid #ffd591' }}>
                            <div style={{ marginBottom: '8px' }}>
                                <strong>基础配额:</strong> {base_quota}
                            </div>
                            <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                                <div>× 分组倍率: {group_ratio}</div>
                                <div>× 充值转换率: {topup_convert_ratio}</div>
                                <div>× 用户折扣率: {user_discount} {user_discount < 1 && '✨'}</div>
                                <div>× 路由折扣率: {route_discount} {route_discount < 1 && '✨'}</div>
                            </div>
                            <div style={{ borderTop: '1px solid #ddd', paddingTop: '8px', fontWeight: 'bold' }}>
                                = 最终配额: {final_quota} (${(final_quota / 500000).toFixed(6)})
                            </div>
                        </div>
                    </div>

                    <div className="billing-note" style={{ marginTop: '8px', textAlign: 'center' }}>
                        <span style={{ color: '#666', fontSize: '12px' }}>
                            动态路由计费 - 详细计费明细
                        </span>
                    </div>
                </div>
            );
        };

        // 根据请求类型创建不同的计费详情模板
        const renderBillingDetails = () => {
            if (!otherObj) return null;

            // 优先使用 billing_detail 结构化数据
            const billingDetail = otherObj.billing_detail;

            if (billingDetail) {
                // 使用结构化的计费明细数据
                return renderStructuredBillingDetails(billingDetail);
            }

            // 兼容旧版本：如果没有 billing_detail，使用原有逻辑
            if (!otherObj.model_ratio) return null;

            const {
                model_ratio = 1,
                completion_ratio = 1,
                group_ratio = 1,
                topup_convert_ratio = 1,
                user_discount = 1
            } = otherObj;

            // 检查是否为动态路由计费（通过content中是否包含"路由折扣率"来判断）
            const isDynamicRoute = record.content && record.content.includes('路由折扣率');
            
            if (isDynamicRoute) {
                // 解析动态路由计费信息
                const contentMatch = record.content.match(/^(\[.*?\])(.+?): (.+?), 调整 (\d+) → (\d+), 分组倍率 ([\d.]+), 充值转换率 ([\d.]+), 用户折扣率 ([\d.]+), 路由折扣率 ([\d.]+), 用时 (\d+)秒$/);
                
                if (contentMatch) {
                    const [, logType, routePath, description, originalQuota, finalQuota, groupRatio, topupRatio, userDiscount, routeDiscount, duration] = contentMatch;
                    
                    // 计算基础价格
                    const basePrice = parseInt(finalQuota) / (500000 * parseFloat(groupRatio) * parseFloat(topupRatio) * parseFloat(userDiscount) * parseFloat(routeDiscount));
                    
                    return (
                        <div className="billing-details" style={{ fontSize: '14px', lineHeight: '1.6' }}>
                            <div className="billing-section" style={{ marginBottom: '16px' }}>
                                <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>
                                    🌐 动态路由计费详情
                                </div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>路由路径:</span>
                                    <span style={{ fontFamily: 'monospace', backgroundColor: '#f5f5f5', padding: '2px 4px', borderRadius: '3px' }}>
                                        {routePath}
                                    </span>
                                </div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>服务描述:</span>
                                    <span>{description}</span>
                                </div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>日志类型:</span>
                                    <Tag color={logType.includes('extra') ? 'green' : logType.includes('refund') ? 'orange' : 'blue'}>
                                        {logType.replace(/[\[\]]/g, '')}
                                    </Tag>
                                </div>
                            </div>
                            
                            <div className="billing-section" style={{ marginBottom: '16px' }}>
                                <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>计费系数</div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>基础价格:</span>
                                    <span>{basePrice.toFixed(6)}</span>
                                </div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>分组倍率:</span>
                                    <span>{groupRatio}</span>
                                </div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>充值转换率:</span>
                                    <span>{topupRatio}</span>
                                </div>
                                <div className="billing-item" style={{ marginBottom: '4px' }}>
                                    <span style={{ fontWeight: '500' }}>用户折扣率:</span>
                                    <span>{userDiscount}</span>
                                </div>
                                <div className="billing-item" style={{ marginBottom: '8px' }}>
                                    <span style={{ fontWeight: '500' }}>路由折扣率:</span>
                                    <span style={{ 
                                        color: parseFloat(routeDiscount) < 1 ? '#52c41a' : '#666',
                                        fontWeight: parseFloat(routeDiscount) < 1 ? 'bold' : 'normal'
                                    }}>
                                        {routeDiscount}
                                        {parseFloat(routeDiscount) < 1 && (
                                            <span style={{ marginLeft: '4px', fontSize: '12px' }}>
                                                (节省 {((1 - parseFloat(routeDiscount)) * 100).toFixed(0)}%)
                                            </span>
                                        )}
                                    </span>
                                </div>
                            </div>
                            
                            <div className="billing-section" style={{ marginBottom: '16px' }}>
                                <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>计费过程</div>
                                <div className="billing-calculation" style={{ 
                                    marginTop: '8px', 
                                    padding: '12px', 
                                    backgroundColor: '#f5f5f5', 
                                    borderRadius: '4px', 
                                    fontFamily: 'monospace',
                                    fontSize: '13px',
                                    lineHeight: '1.5'
                                }}>
                                    <div style={{ marginBottom: '4px' }}>
                                        基础价格: {basePrice.toFixed(6)}
                                    </div>
                                    <div style={{ marginBottom: '4px' }}>
                                        × 分组倍率: {groupRatio}
                                    </div>
                                    <div style={{ marginBottom: '4px' }}>
                                        × 充值转换率: {topupRatio}
                                    </div>
                                    <div style={{ marginBottom: '4px' }}>
                                        × 用户折扣率: {userDiscount}
                                    </div>
                                    <div style={{ marginBottom: '8px', color: parseFloat(routeDiscount) < 1 ? '#52c41a' : '#666' }}>
                                        × 路由折扣率: {routeDiscount}
                                        {parseFloat(routeDiscount) < 1 && ' ✨'}
                                    </div>
                                    <div style={{ borderTop: '1px solid #ddd', paddingTop: '8px', fontWeight: 'bold' }}>
                                        = ${(parseInt(finalQuota) / 500000).toFixed(6)}
                                    </div>
                                </div>
                                <div className="billing-item" style={{ marginTop: '8px' }}>
                                    <span style={{ fontWeight: '500' }}>处理时长:</span>
                                    <span>{duration}秒</span>
                                </div>
                                <div className="billing-note" style={{ marginTop: '8px', textAlign: 'center' }}>
                                    <span style={{ color: '#666', fontSize: '12px' }}>
                                        动态路由计费 - 支持个性化路由折扣
                                    </span>
                                </div>
                            </div>
                        </div>
                    );
                }
            }

            // 计算基础倍率
            const baseRatio = model_ratio * group_ratio;
            const finalRatio = baseRatio * topup_convert_ratio * user_discount;

            // 根据请求类型和计费类型定制计费详情
            switch (requestType) {
                case 'image':
                    if (billingType === 'by_count') {
                        // 按次计费的图片生成
                        const fixedPrice = record.quota / (500000 * finalRatio);
                        return (
                            <div className="billing-details">
                                <div className="billing-item">
                                    <span className="billing-label">计费方式:</span>
                                    <span className="billing-value">按次计费</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">固定价格:</span>
                                    <span className="billing-value">{fixedPrice.toFixed(6)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">分组倍率:</span>
                                    <span className="billing-value">{group_ratio.toFixed(2)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">充值转换率:</span>
                                    <span className="billing-value">{topup_convert_ratio.toFixed(4)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">用户折扣:</span>
                                    <span className="billing-value">{user_discount.toFixed(2)}</span>
                                </div>
                                <div className="billing-calculation">
                                    <span>计算: {fixedPrice.toFixed(6)} × 500000 × {group_ratio.toFixed(2)} × {topup_convert_ratio.toFixed(4)} × {user_discount.toFixed(2)} = {record.quota}</span>
                                </div>
                            </div>
                        );
                    } else {
                        // 按量计费的图片处理
                        const promptTokens = record.prompt_tokens || 0;
                        const completionTokens = record.completion_tokens || 0;
                        
                        // 检查是否有usage信息在other字段中
                        let detailsContent: React.ReactNode = null;
                        const usage = otherObj?.usage;
                        
                        if (usage && usage.input_tokens_details && (usage.input_tokens_details.text_tokens || usage.input_tokens_details.image_tokens)) {
                            const textTokens = usage.input_tokens_details.text_tokens || 0;
                            const imageTokens = usage.input_tokens_details.image_tokens || 0;
                            const imageRatio = 2.0; // 图片倍率
                            
                            // 计算价格信息
                            const baseInputPrice = 5; // $5 / 1M tokens
                            const outputPrice = baseInputPrice * completion_ratio; // 输出价格 = 基础价格 × 补全倍率
                            const imageInputPrice = baseInputPrice * imageRatio; // 图片输入价格 = 基础价格 × 图片倍率
                            
                            detailsContent = (
                                <div className="billing-details" style={{ fontSize: '14px', lineHeight: '1.6' }}>
                                    <div className="billing-section" style={{ marginBottom: '16px' }}>
                                        <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>日志详情</div>
                                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                                            <span>模型倍率 {model_ratio.toFixed(1)}，输出倍率 {completion_ratio.toFixed(0)}，分组倍率 {group_ratio.toFixed(0)}</span>
                                        </div>
                                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                                            <span>充值转换率: {topup_convert_ratio.toFixed(4)}</span>
                                        </div>
                                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                                            <span>用户折扣: {user_discount.toFixed(2)}</span>
                                        </div>
                                    </div>
                                    
                                    <div className="billing-section" style={{ marginBottom: '16px' }}>
                                        <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>计费过程</div>
                                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                                            <span style={{ fontWeight: '500' }}>输入价格：</span>
                                            <span>${baseInputPrice} / 1M tokens</span>
                                        </div>
                                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                                            <span style={{ fontWeight: '500' }}>输出价格：</span>
                                            <span>${baseInputPrice} × {completion_ratio.toFixed(0)} = ${outputPrice} / 1M tokens (补全倍率: {completion_ratio.toFixed(0)})</span>
                                        </div>
                                        <div className="billing-item" style={{ marginBottom: '4px' }}>
                                            <span style={{ fontWeight: '500' }}>图片输入价格：</span>
                                            <span>${baseInputPrice} × {imageRatio.toFixed(0)} = ${imageInputPrice} / 1M tokens (图片倍率: {imageRatio.toFixed(0)})</span>
                                        </div>
                                        <div className="billing-calculation" style={{ marginTop: '12px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px', fontFamily: 'monospace' }}>
                                            <span>(输入 {textTokens} tokens + 图片输入 {imageTokens} tokens × {imageRatio.toFixed(0)} / 1M tokens × ${baseInputPrice} + 输出 {completionTokens} tokens / 1M tokens × ${outputPrice}) × 分组倍率 {group_ratio.toFixed(0)} × 充值转换率 {topup_convert_ratio.toFixed(4)} × 用户折扣 {user_discount.toFixed(2)} = ${(record.quota / 500000).toFixed(6)}</span>
                                        </div>
                                        <div className="billing-note" style={{ marginTop: '8px', textAlign: 'center' }}>
                                            <span style={{ color: '#666', fontSize: '12px' }}>仅供参考，以实际扣费为准</span>
                                        </div>
                                    </div>
                                </div>
                            );
                        }
                        
                        if (detailsContent) {
                            return detailsContent;
                        }
                        
                        // 向后兼容：尝试从record.content中解析usage信息
                        try {
                            const contentObj = JSON.parse(record.content || '{}');
                            const contentUsage = contentObj.usage || {};
                            const inputTokensDetails = contentUsage.input_tokens_details;
                            
                            if (inputTokensDetails && (inputTokensDetails.text_tokens || inputTokensDetails.image_tokens)) {
                                const textTokens = inputTokensDetails.text_tokens || 0;
                                const imageTokens = inputTokensDetails.image_tokens || 0;
                                const imageRatio = 2.0;
                                const baseInputPrice = 5;
                                const outputPrice = baseInputPrice * completion_ratio;
                                const imageInputPrice = baseInputPrice * imageRatio;
                                
                                return (
                                    <div className="billing-details" style={{ fontSize: '14px', lineHeight: '1.6' }}>
                                        <div className="billing-section" style={{ marginBottom: '16px' }}>
                                            <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>日志详情</div>
                                            <div className="billing-item" style={{ marginBottom: '4px' }}>
                                                <span>模型倍率 {model_ratio.toFixed(1)}，输出倍率 {completion_ratio.toFixed(0)}，分组倍率 {group_ratio.toFixed(0)}</span>
                                            </div>
                                            <div className="billing-item" style={{ marginBottom: '4px' }}>
                                                <span>充值转换率: {topup_convert_ratio.toFixed(4)}</span>
                                            </div>
                                            <div className="billing-item" style={{ marginBottom: '4px' }}>
                                                <span>用户折扣: {user_discount.toFixed(2)}</span>
                                            </div>
                                        </div>
                                        
                                        <div className="billing-section" style={{ marginBottom: '16px' }}>
                                            <div className="billing-title" style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>计费过程</div>
                                            <div className="billing-item" style={{ marginBottom: '4px' }}>
                                                <span style={{ fontWeight: '500' }}>输入价格：</span>
                                                <span>${baseInputPrice} / 1M tokens</span>
                                            </div>
                                            <div className="billing-item" style={{ marginBottom: '4px' }}>
                                                <span style={{ fontWeight: '500' }}>输出价格：</span>
                                                <span>${baseInputPrice} × {completion_ratio.toFixed(0)} = ${outputPrice} / 1M tokens (补全倍率: {completion_ratio.toFixed(0)})</span>
                                            </div>
                                            <div className="billing-item" style={{ marginBottom: '4px' }}>
                                                <span style={{ fontWeight: '500' }}>图片输入价格：</span>
                                                <span>${baseInputPrice} × {imageRatio.toFixed(0)} = ${imageInputPrice} / 1M tokens (图片倍率: {imageRatio.toFixed(0)})</span>
                                            </div>
                                            <div className="billing-calculation" style={{ marginTop: '12px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px', fontFamily: 'monospace' }}>
                                                <span>(输入 {textTokens} tokens + 图片输入 {imageTokens} tokens × {imageRatio.toFixed(0)} / 1M tokens × ${baseInputPrice} + 输出 {completionTokens} tokens / 1M tokens × ${outputPrice}) × 分组倍率 {group_ratio.toFixed(0)} × 充值转换率 {topup_convert_ratio.toFixed(4)} × 用户折扣 {user_discount.toFixed(2)} = ${(record.quota / 500000).toFixed(6)}</span>
                                            </div>
                                            <div className="billing-note" style={{ marginTop: '8px', textAlign: 'center' }}>
                                                <span style={{ color: '#666', fontSize: '12px' }}>仅供参考，以实际扣费为准</span>
                                            </div>
                                        </div>
                                    </div>
                                );
                            }
                        } catch (e) {
                            // 解析失败，忽略
                        }
                        
                        // 标准图片处理计费
                        const totalCost = (promptTokens + completionTokens * completion_ratio) * finalRatio;
                        return (
                            <div className="billing-details">
                                <div className="billing-item">
                                    <span className="billing-label">计费方式:</span>
                                    <span className="billing-value">按量计费 (图片处理)</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">输入tokens:</span>
                                    <span className="billing-value">{promptTokens}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">输出tokens:</span>
                                    <span className="billing-value">{completionTokens}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">补全倍率:</span>
                                    <span className="billing-value">{completion_ratio.toFixed(4)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">模型倍率:</span>
                                    <span className="billing-value">{model_ratio.toFixed(4)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">分组倍率:</span>
                                    <span className="billing-value">{group_ratio.toFixed(2)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">充值转换率:</span>
                                    <span className="billing-value">{topup_convert_ratio.toFixed(4)}</span>
                                </div>
                                <div className="billing-item">
                                    <span className="billing-label">用户折扣:</span>
                                    <span className="billing-value">{user_discount.toFixed(2)}</span>
                                </div>
                                <div className="billing-calculation">
                                    <span>计算: ({promptTokens} + {completionTokens} × {completion_ratio.toFixed(4)}) × {model_ratio.toFixed(4)} × {group_ratio.toFixed(2)} × {topup_convert_ratio.toFixed(4)} × {user_discount.toFixed(2)} = {record.quota}</span>
                                </div>
                            </div>
                        );
                    }
                case 'audio':
                    return (
                        <div style={{fontSize: '14px', lineHeight: '1.6'}}>
                            <p><strong>🎵 音频处理计费详情</strong></p>
                            <p><strong>模型倍率:</strong> {model_ratio}</p>
                            <p><strong>分组倍率:</strong> {group_ratio}</p>
                            <p><strong>充值转换率:</strong> {topup_convert_ratio}</p>
                            <p><strong>用户折扣率:</strong> {user_discount}</p>
                            {record.prompt_tokens > 0 && (
                                <p><strong>音频时长:</strong> {record.prompt_tokens}秒</p>
                            )}
                            <p>
                                <strong>计算过程:</strong><br/>
                                {billingType === 'by_count' ? 
                                    `固定价格 × ${model_ratio} × ${group_ratio} × ${topup_convert_ratio} × ${user_discount} × 500000 = ${record.quota}` :
                                    `${record.prompt_tokens || 'tokens'} × ${model_ratio} × ${group_ratio} × ${topup_convert_ratio} × ${user_discount} / 500000 = ${(record.quota / 500000).toFixed(6)}`
                                }
                            </p>
                            <p><strong>扣费金额:</strong> ${(record.quota / 500000).toFixed(6)}</p>
                            <p style={{color: '#999'}}>音频处理按时长或tokens计费</p>
                        </div>
                    );
                case 'embedding':
                    const embeddingCacheBilling = otherObj?.cache_billing;
                    const embeddingHasCacheInfo = embeddingCacheBilling && embeddingCacheBilling.cache_enabled;

                    return (
                        <div style={{fontSize: '14px', lineHeight: '1.6'}}>
                            <p><strong>🔍 向量嵌入计费详情</strong></p>
                            <p><strong>模型倍率:</strong> {model_ratio}</p>
                            <p><strong>分组倍率:</strong> {group_ratio}</p>
                            <p><strong>充值转换率:</strong> {topup_convert_ratio}</p>
                            <p><strong>用户折扣率:</strong> {user_discount}</p>
                            <p><strong>输入 tokens:</strong> {record.prompt_tokens}</p>

                            {/* 如果有cache信息，显示cache相关的详细计费 */}
                            {embeddingHasCacheInfo && (
                                <div style={{marginTop: '12px', padding: '8px', backgroundColor: '#f0f8ff', borderRadius: '4px', border: '1px solid #d1ecf1'}}>
                                    <p><strong>🗄️ 缓存计费详情</strong></p>
                                    {embeddingCacheBilling.cache_creation_tokens > 0 && (
                                        <p style={{marginLeft: '20px'}}>
                                            <strong>缓存创建:</strong> {model_ratio.toFixed(2)} × {embeddingCacheBilling.cache_creation_ratio}x × {embeddingCacheBilling.cache_creation_tokens} tokens = ${((model_ratio * embeddingCacheBilling.cache_creation_ratio * embeddingCacheBilling.cache_creation_tokens) / 500000).toFixed(6)}
                                        </p>
                                    )}
                                    {embeddingCacheBilling.cache_read_tokens > 0 && (
                                        <p style={{marginLeft: '20px'}}>
                                            <strong>缓存读取:</strong> {model_ratio.toFixed(2)} × {embeddingCacheBilling.cache_read_ratio}x × {embeddingCacheBilling.cache_read_tokens} tokens = ${((model_ratio * embeddingCacheBilling.cache_read_ratio * embeddingCacheBilling.cache_read_tokens) / 500000).toFixed(6)}
                                        </p>
                                    )}
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>缓存总费用:</strong> ${((model_ratio * embeddingCacheBilling.total_cache_cost) / 500000).toFixed(6)}
                                    </p>
                                </div>
                            )}

                            <p>
                                <strong>计算过程:</strong><br/>
                                {embeddingHasCacheInfo ? (
                                    <>
                                        基础费用: {record.prompt_tokens} × {model_ratio} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000<br/>
                                        缓存费用: {(model_ratio * embeddingCacheBilling.total_cache_cost).toFixed(2)} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000<br/>
                                        总计: ${(record.quota / 500000).toFixed(6)}
                                    </>
                                ) : (
                                    <>
                                        {record.prompt_tokens} × {model_ratio} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000 = ${(record.quota / 500000).toFixed(6)}
                                    </>
                                )}
                            </p>
                            <p><strong>扣费金额:</strong> ${(record.quota / 500000).toFixed(6)}</p>
                            <p style={{color: '#999'}}>向量嵌入按输入tokens计费</p>
                        </div>
                    );
                case 'moderation':
                    const moderationCacheBilling = otherObj?.cache_billing;
                    const moderationHasCacheInfo = moderationCacheBilling && moderationCacheBilling.cache_enabled;

                    return (
                        <div style={{fontSize: '14px', lineHeight: '1.6'}}>
                            <p><strong>🛡️ 内容审核计费详情</strong></p>
                            <p><strong>模型倍率:</strong> {model_ratio}</p>
                            <p><strong>分组倍率:</strong> {group_ratio}</p>
                            <p><strong>充值转换率:</strong> {topup_convert_ratio}</p>
                            <p><strong>用户折扣率:</strong> {user_discount}</p>
                            <p><strong>输入 tokens:</strong> {record.prompt_tokens}</p>

                            {/* 如果有cache信息，显示cache相关的详细计费 */}
                            {moderationHasCacheInfo && (
                                <div style={{marginTop: '12px', padding: '8px', backgroundColor: '#f0f8ff', borderRadius: '4px', border: '1px solid #d1ecf1'}}>
                                    <p><strong>🗄️ 缓存计费详情</strong></p>
                                    {moderationCacheBilling.cache_creation_tokens > 0 && (
                                        <p style={{marginLeft: '20px'}}>
                                            <strong>缓存创建:</strong> {model_ratio.toFixed(2)} × {moderationCacheBilling.cache_creation_ratio}x × {moderationCacheBilling.cache_creation_tokens} tokens = ${((model_ratio * moderationCacheBilling.cache_creation_ratio * moderationCacheBilling.cache_creation_tokens) / 500000).toFixed(6)}
                                        </p>
                                    )}
                                    {moderationCacheBilling.cache_read_tokens > 0 && (
                                        <p style={{marginLeft: '20px'}}>
                                            <strong>缓存读取:</strong> {model_ratio.toFixed(2)} × {moderationCacheBilling.cache_read_ratio}x × {moderationCacheBilling.cache_read_tokens} tokens = ${((model_ratio * moderationCacheBilling.cache_read_ratio * moderationCacheBilling.cache_read_tokens) / 500000).toFixed(6)}
                                        </p>
                                    )}
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>缓存总费用:</strong> ${((model_ratio * moderationCacheBilling.total_cache_cost) / 500000).toFixed(6)}
                                    </p>
                                </div>
                            )}

                            <p>
                                <strong>计算过程:</strong><br/>
                                {moderationHasCacheInfo ? (
                                    <>
                                        基础费用: {record.prompt_tokens} × {model_ratio} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000<br/>
                                        缓存费用: {(model_ratio * moderationCacheBilling.total_cache_cost).toFixed(2)} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000<br/>
                                        总计: ${(record.quota / 500000).toFixed(6)}
                                    </>
                                ) : (
                                    <>
                                        {record.prompt_tokens} × {model_ratio} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000 = ${(record.quota / 500000).toFixed(6)}
                                    </>
                                )}
                            </p>
                            <p><strong>扣费金额:</strong> ${(record.quota / 500000).toFixed(6)}</p>
                            <p style={{color: '#999'}}>内容审核按输入tokens计费</p>
                        </div>
                    );
                case 'text':
                default:
                    // 检查是否有cache相关信息
                    const cacheBilling = otherObj?.cache_billing;
                    const hasCacheInfo = cacheBilling && cacheBilling.cache_enabled;

                    // 验证计算结果
                    const calculatedQuota = Math.ceil((record.prompt_tokens + record.completion_tokens * completion_ratio) * finalRatio);
                    const inputPrice = model_ratio * 2; // 假设基础价格为$2/1M tokens
                    const outputPrice = model_ratio * 2 * completion_ratio;

                    return (
                        <div style={{fontSize: '14px', lineHeight: '1.6'}}>
                            <p><strong>💬 文本对话计费详情</strong></p>
                            <p><strong>模型倍率:</strong> {model_ratio}</p>
                            <p style={{marginLeft: '20px', color: '#666'}}>
                                对应官方输入价格: ${inputPrice.toFixed(2)} / 1M tokens
                            </p>
                            <p><strong>补全倍率:</strong> {completion_ratio}</p>
                            <p style={{marginLeft: '20px', color: '#666'}}>
                                对应官方输出价格: ${outputPrice.toFixed(2)} / 1M tokens
                            </p>
                            <p><strong>分组倍率:</strong> {group_ratio}</p>
                            <p><strong>充值转换率:</strong> {topup_convert_ratio}</p>
                            <p><strong>用户折扣率:</strong> {user_discount}</p>
                            <p><strong>输入 tokens:</strong> {record.prompt_tokens}</p>
                            <p><strong>输出 tokens:</strong> {record.completion_tokens}</p>

                            {/* 如果有cache信息，显示cache相关的详细计费 */}
                            {hasCacheInfo && (
                                <div style={{marginTop: '12px', padding: '8px', backgroundColor: '#f0f8ff', borderRadius: '4px', border: '1px solid #d1ecf1'}}>
                                    <p><strong>🗄️ 缓存计费详情</strong></p>
                                    {cacheBilling.cache_creation_tokens > 0 && (
                                        <p style={{marginLeft: '20px'}}>
                                            <strong>缓存创建:</strong> {model_ratio.toFixed(2)} × {cacheBilling.cache_creation_ratio}x × {cacheBilling.cache_creation_tokens} tokens = ${((model_ratio * cacheBilling.cache_creation_ratio * cacheBilling.cache_creation_tokens) / 500000).toFixed(6)}
                                        </p>
                                    )}
                                    {cacheBilling.cache_read_tokens > 0 && (
                                        <p style={{marginLeft: '20px'}}>
                                            <strong>缓存读取:</strong> {model_ratio.toFixed(2)} × {cacheBilling.cache_read_ratio}x × {cacheBilling.cache_read_tokens} tokens = ${((model_ratio * cacheBilling.cache_read_ratio * cacheBilling.cache_read_tokens) / 500000).toFixed(6)}
                                        </p>
                                    )}
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>缓存总费用:</strong> ${((model_ratio * cacheBilling.total_cache_cost) / 500000).toFixed(6)}
                                    </p>
                                    <p style={{marginLeft: '20px', color: '#666', fontSize: '12px'}}>
                                        缓存创建费率高(1.25x)，读取费率低(0.1x)，重复使用可节省费用
                                    </p>
                                </div>
                            )}

                            <p>
                                <strong>计算过程:</strong><br/>
                                {hasCacheInfo ? (
                                    <>
                                        基础费用: ({record.prompt_tokens} + {record.completion_tokens} × {completion_ratio}) × {model_ratio} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000<br/>
                                        缓存费用: {(model_ratio * cacheBilling.total_cache_cost).toFixed(2)} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000<br/>
                                        总计: ${(record.quota / 500000).toFixed(6)}
                                    </>
                                ) : (
                                    <>
                                        ({record.prompt_tokens} + {record.completion_tokens} × {completion_ratio}) × {model_ratio} × {group_ratio} × {topup_convert_ratio} × {user_discount} / 500000 = ${(calculatedQuota / 500000).toFixed(6)}
                                    </>
                                )}
                            </p>
                            <p><strong>实际扣费:</strong> ${(record.quota / 500000).toFixed(6)}</p>
                            {Math.abs(calculatedQuota - record.quota) > 1 && (
                                <p style={{color: '#ff9800', fontSize: '12px'}}>
                                    注意：计算结果与实际扣费存在差异，可能由于最小扣费限制或其他计费规则
                                </p>
                            )}
                            <p style={{color: '#999'}}>仅供参考，以实际扣费为准</p>
                        </div>
                    );
            }
        };

        // 如果有倍率信息，显示增强的 Popover
        if (otherObj && otherObj.model_ratio) {
            return (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {renderRequestType(requestType, record)}
                    <Popover
                        content={renderBillingDetails()}
                        placement="left"
                        trigger="hover"
                        overlayStyle={{ maxWidth: '400px' }}
                    >
                        <span style={{ flex: 1, cursor: 'pointer' }}>
                            {record.content}
                        </span>
                    </Popover>
                </div>
            );
        } else {
            // 没有倍率信息时，显示简单的 content 字段内容
            return (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {renderRequestType(requestType, record)}
                    <Tooltip
                        title={record.content}
                        mouseEnterDelay={0}
                        placement="left"
                    >
                        <span
                            style={{cursor: 'pointer', color: '#1890ff', flex: 1}}
                            onClick={(e) => {
                                e.stopPropagation();
                                setCurrentContent(record.content);
                                setIsModalVisible(true);
                            }}
                        >
                            {record.content}
                        </span>
                    </Tooltip>
                </div>
            );
        }
    };

    const columns_admin = [
        {
            title: t('logsTable.time'),
            width: '11.5%',
            dataIndex: 'created_time',
            filters: true,
            order: 0,
            hideInSearch: true,
            valueType: 'date',
            render: (_text: any, record: {
                created_at: number;
                ip: any;
                request_id: any;
                user_id: any;
                username: any;
                token_key: any;
                token_name: any;
            }) => {
                let date = moment(record.created_at * 1000);
                if (date.year() === 1970) {
                    return '';
                } else {
                    return (
                        <Popover
                            title={t('logsTable.moreInfo')}
                            content={[
                                <>
                                    <p>{t('logsTable.time')}: {date.format('YYYY/MM/DD HH:mm:ss')}</p>
                                    <p>{t('logsTable.ip')}: {record.ip}</p>
                                    <p>
                                        {t('logsTable.requestId')}:
                                        <Button
                                            type="link"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleRequestIdClick(record.request_id);
                                            }}
                                            style={{padding: '0 4px'}}
                                        >
                                            {record.request_id}
                                        </Button>
                                    </p>
                                    <p>{t('logsTable.username')}: {record.username}</p>
                                    <p>{t('logsTable.userId')}: {record.user_id}</p>
                                    <p>{t('logsTable.tokenName')}: {record.token_name}</p>
                                    <p>{t('logsTable.token')}: sk-{record.token_key}</p>
                                </>,
                            ]}>
                            {timestamp2string(record.created_at)}
                        </Popover>
                    );
                }
            },
        },
        {
            title: t('logsTable.time'),
            dataIndex: 'created_time',
            valueType: 'dateTimeRange',
            hideInTable: true,
            colSize: 2,
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        start_timestamp: new Date(value[0]).getTime() / 1000,
                        end_timestamp: new Date(value[1]).getTime() / 1000
                    };
                },
                order: 99,
            },
            fieldProps: {
                ranges: {
                    [t('logsTable.today')]: [moment().startOf('day'), moment().endOf('day')],
                    [t('logsTable.lastHour')]: [moment().subtract(1, 'hours'), moment()],
                    [t('logsTable.last3Hours')]: [moment().subtract(3, 'hours'), moment()],
                    [t('logsTable.lastDay')]: [moment().subtract(1, 'days'), moment()],
                    [t('logsTable.last3Days')]: [moment().subtract(3, 'days'), moment()],
                    [t('logsTable.last7Days')]: [moment().subtract(7, 'days'), moment()],
                    [t('logsTable.lastMonth')]: [moment().subtract(1, 'months'), moment()],
                    [t('logsTable.last3Months')]: [moment().subtract(3, 'months'), moment()]
                }
            }
        },
        {
            title: t('logsTable.type'),
            dataIndex: 'type',
            width: '8%',
            fieldProps: {mode: 'multiple'},
            valueEnum: Object.fromEntries(
                LOGS_TYPE_OPTIONS.map(option => [
                    option.value,
                    {text: t(`logsTable.type${LOGS_TYPE[option.value].text}`)}
                ])
            ),
            render: (_, record) => {
                const status = LOGS_TYPE[record.type];
                return status ? (
                    <Tag color={status.color}>
                        {t(`logsTable.type${status.text}`)}
                    </Tag>
                ) : (
                    <Tag color="default">{t('logsTable.typeUnknown')}</Tag>
                );
            },
            search: {
                order: 99,
            }
        },
        {
            title: t('logsTable.channelId'),
            dataIndex: 'channel',
            hideInTable: true,
        },
        {
            title: t('logsTable.channelName'),
            dataIndex: 'channel_name',
            hideInTable: true,
        },
        {
            title: t('logsTable.channel'),
            width: '16.5%',
            key: 'channel_info',
            search: false,
            render: (_channel_name, record) => {
                if (!record.channel_name || !record.channel) return '';
                return <Tag icon={<ControlFilled/>} color='default'
                            bordered={false}>{`${record.channel_name}（#${record.channel}）`}</Tag>;
            }
        },
        {
            title: t('logsTable.username'),
            width: '11.5%',
            dataIndex: 'username',
            render: (username) => {
                if (!username) return '';
                return (
                    <Tooltip title={`${username} (${t('message.clipboard.clickToCopy')})`}>
                        <Tag
                            icon={<IdcardFilled/>}
                            color='purple'
                            bordered={false}
                            onClick={() => {
                                navigator.clipboard.writeText(username);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{cursor: 'pointer'}}
                        >
                            {username}
                        </Tag>
                    </Tooltip>
                );
            },
            disable: true
        },
        {
            title: t('logsTable.token'),
            width: '11.5%',
            dataIndex: 'token_name',
            ellipsis: true,
            render: (token_name, record) => {
                if (!token_name) return '';
                const displayName = typeof record.token_name === 'string' ? record.token_name : String(record.token_name);
                return (
                    <Tooltip title={`${displayName} (${t('message.clipboard.clickToCopy')})`}>
                        <Tag
                            icon={<LockFilled/>}
                            color='cyan'
                            bordered={false}
                            onClick={() => {
                                navigator.clipboard.writeText(displayName);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{
                                cursor: 'pointer',
                                maxWidth: '100%',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                            }}
                        >
                            {displayName}
                        </Tag>
                    </Tooltip>
                );
            }
        },
        {
            title: t('logsTable.tokenGroup'),
            width: '17.5%',
            dataIndex: 'token_group',
            render: (token_group, record) => {
                if (!token_group) return '';

                if (groups.length === 0) {
                    return (
                        <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                            <span style={{flex: 1, overflow: 'hidden', textOverflow: 'ellipsis'}}>{token_group}</span>
                            {loading && <LoadingOutlined style={{fontSize: 14}}/>}
                        </div>
                    );
                }

                const group = groups.find(g => g.name === token_group);
                if (!group) return token_group;
                const isCurrentGroup = group.name === userState.user.group;
                const ratio = isCurrentGroup ? 
                    group.current_group_ratio : 
                    (group.convert_ratio || 1) * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);

                // 获取最大小数位数
                const maxDecimalPlaces = Math.max(
                    getNumberDecimalPlaces(group.convert_ratio || 1),
                    getNumberDecimalPlaces(group.current_group_ratio || 1),
                    getNumberDecimalPlaces(group.current_topup_ratio || 1)
                );

                // 使用动态精度
                const formattedRatio = ratio.toFixed(maxDecimalPlaces);

                return (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line'}}>
                            <p><strong>分组名称:</strong> {group.display_name}</p>
                            <p><strong>分组标识:</strong> {group.name}</p>
                            <p><strong>转换倍率:</strong> {group.convert_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>分组倍率:</strong> {group.current_group_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>充值倍率:</strong> {group.current_topup_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>最终倍率:</strong> {formattedRatio}x</p>
                        </div>
                    }>
                        <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                            gap: '8px'
                        }}>
                            <span style={{
                                flex: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                minWidth: 0
                            }}>
                                {group.display_name || token_group}
                            </span>
                            <span style={{
                                padding: '2px 8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                backgroundColor: Number(formattedRatio) > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                color: Number(formattedRatio) > 1 ? '#ff4d4f' : '#52c41a',
                                flexShrink: 0
                            }}>
                                {formattedRatio}x
                            </span>
                        </div>
                    </Tooltip>
                );
            },
            renderFormItem: (_item, {defaultRender, ...rest}) => {
                return <Select
                    {...rest}
                    placeholder={t('logsTable.selectGroup')}
                    style={{width: '100%'}}
                    allowClear={true}
                    options={groups.map(group => {
                        const isCurrentGroup = group.name === userState.user.group;
                        const ratio = isCurrentGroup ? 
                            group.current_group_ratio : 
                            (group.convert_ratio || 1) * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);
                        // 获取最大小数位数
                        const maxDecimalPlaces = Math.max(
                            getNumberDecimalPlaces(group.convert_ratio || 1),
                            getNumberDecimalPlaces(group.current_group_ratio || 1),
                            getNumberDecimalPlaces(group.current_topup_ratio || 1)
                        );
                        // 使用动态精度
                        const formattedRatio = ratio.toFixed(maxDecimalPlaces);
                        return {
                            label: (
                                <div style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    width: '100%',
                                    gap: '8px',
                                    whiteSpace: 'nowrap',
                                    overflow: 'visible'
                                }}>
                                    <span style={{
                                        flex: 1,
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        minWidth: 0
                                    }}>
                                        {group.display_name}
                                    </span>
                                    <span style={{
                                        padding: '2px 8px',
                                        borderRadius: '4px',
                                        fontSize: '12px',
                                        backgroundColor: Number(formattedRatio) > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                        color: Number(formattedRatio) > 1 ? '#ff4d4f' : '#52c41a',
                                        flexShrink: 0
                                    }}>
                                        {formattedRatio}x
                                    </span>
                                </div>
                            ),
                            value: group.name
                        };
                    })}
                    dropdownStyle={{
                        minWidth: '300px',
                        maxWidth: '500px'
                    }}
                />;
            }
        },
        {
            title: t('logsTable.model'),
            width: '14%',
            dataIndex: 'model_name',
            render: (model_name) => {
                if (!model_name) return '';
                return (
                    <Tooltip title={`${model_name} (${t('message.clipboard.clickToCopy')})`}>
                        <div
                            onClick={() => {
                                navigator.clipboard.writeText(model_name as string);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{cursor: 'pointer'}}
                        >
                            {renderModelTags(model_name as string)}
                        </div>
                    </Tooltip>
                );
            },
            renderFormItem: (_item, {defaultRender, ...rest}) => {
                return <AutoComplete
                    options={AUTOCOMPLETE_MODEL_NAMES.map(name => ({value: name}))}
                    {...rest}
                    placeholder={t('logsTable.modelPlaceholder')}
                    filterOption={(inputValue, option) => {
                        if (!option?.value) return false;
                        return option.value.toLowerCase().includes(inputValue.toLowerCase());
                    }}
                    showSearch={true}
                    allowClear={true}
                    style={{width: '100%'}}
                />;
            },
            search: {
                order: 98,
            },
        },
        {
            title: t('logsTable.info'),
            width: '8.5%',
            key: 'request_info',
            hideInSearch: true,
            render: (_, record) => {
                return (
                    <>
                        {renderIsStream(record)}
                        {renderTotalDuration(record, durationType)}
                    </>
                )
            }
        },
        {
            title: t('logsTable.isStream'),
            dataIndex: 'is_stream',
            hideInTable: true,
            valueEnum: {
                'true': {text: t('common.yes')},
                'false': {text: t('common.no')}
            }
        },
        {
            title: t('logsTable.prompt'),
            width: '5.5%',
            dataIndex: 'prompt_tokens',
            render: (_, record) => record.prompt_tokens === 0 ? '' : record.prompt_tokens,
            valueType: 'digitRange',
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        prompt_tokens_min: value[0],
                        prompt_tokens_max: value[1]
                    };
                }
            },
        },
        {
            title: t('logsTable.completion'),
            width: '5.5%',
            dataIndex: 'completion_tokens',
            render: (_, record) => record.completion_tokens === 0 ? '' : record.completion_tokens,
            valueType: 'digitRange',
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        completion_tokens_min: value[0],
                        completion_tokens_max: value[1]
                    };
                }
            },
        },
        {
            title: t('logsTable.consumption'),
            width: '9%',
            dataIndex: 'quota',
            search: false,
            render: (_, record) => {
                if (record.quota === 0 || typeof record.quota === 'undefined' || record.quota === null) {
                    return '';
                }

                const revenue = record.quota / 500000;
                const cost = record.cost_quota ? record.cost_quota / 500000 : 0;
                const profit = revenue - cost;

                return (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line'}}>
                            {`收入: $${revenue.toFixed(6)}
成本: $${cost.toFixed(6)}
利润: $${profit.toFixed(6)}`}
                        </div>
                    }>
                        <span style={{cursor: 'pointer'}}>
                            ${revenue.toFixed(6)}
                        </span>
                    </Tooltip>
                );
            }
        },
        {
            title: t('logsTable.consumptionRange'),
            dataIndex: 'quota_range',
            hideInTable: true,
            valueType: 'digitRange',
            fieldProps: {
                precision: 6,
                formatter: (value) => value ? `$${value}` : '',
                parser: (value) => value ? value.replace(/\$\s?|(,*)/g, '') : '',
                placeholder: ['$最小值', '$最大值'],
            },
            search: {
                transform: (value) => {
                    if (!value) return {};
                    // 将美元转换为 quota (1美元 = 500000 quota)
                    return {
                        quota_min: value[0] ? Math.floor(value[0] * 500000) : undefined,
                        quota_max: value[1] ? Math.ceil(value[1] * 500000) : undefined,
                    };
                }
            },
        },
        {
            title: t('logsTable.retry'),
            width: '6.5%',
            key: 'action-retry',
            search: false,
            render: (_, record) => {
                if (!record.other) return '';
                let otherObj;
                try {
                    otherObj = JSON.parse(record.other);
                } catch (e) {
                    console.error('Failed to parse other field:', e);
                    return '';
                }
                const admin_info = otherObj?.admin_info;
                if (!admin_info) return '';
                const use_channel = admin_info.use_channel;
                const retry_durations = admin_info.retry_durations;
                if (!use_channel || use_channel.length === 0) return '';

                return (
                    <Popover
                        title={t('logsTable.retryChannelList')}
                        content={
                            <Space direction="vertical">
                                <div>
                                    {use_channel.map((channel: number, index: number) => (
                                        <React.Fragment key={channel}>
                                            <Tag color='blue'>{channel}</Tag>
                                            {index < use_channel.length - 1 && (
                                                <span style={{margin: '0 5px'}}>→</span>
                                            )}
                                        </React.Fragment>
                                    ))}
                                </div>
                                {retry_durations && retry_durations.length > 0 && (
                                    <div style={{marginTop: '8px'}}>
                                        <Typography.Text type="secondary">
                                            {t('logsTable.retryDurations')}:
                                        </Typography.Text>
                                        {retry_durations.map((retry: any, index: number) => (
                                            <div key={index} style={{marginTop: '4px'}}>
                                                <Tag color="cyan">
                                                    {t('logsTable.channel')}: {retry.channel}
                                                </Tag>
                                                <Tag color="green">
                                                    {t('logsTable.duration')}: {retry.duration}s
                                                </Tag>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </Space>
                        }
                    >
                        <Button type='link'>
                            {t('logsTable.retry')}
                            {retry_durations && retry_durations.length > 0 && (
                                <sup style={{color: '#ff4d4f'}}>
                                    {retry_durations.length}
                                </sup>
                            )}
                        </Button>
                    </Popover>
                );
            },
        },
        {
            title: t('logsTable.ip'),
            width: '12%',
            dataIndex: 'ip',
            ellipsis: true,
            render: (ip: any, record: any) => {
                const ipValue = record.ip;
                if (!ipValue || typeof ipValue !== 'string') return '';

                const ipContent = record.remote_ip && record.remote_ip !== ipValue ? (
                    <Tooltip title={`${t('logsTable.remoteIp')}: ${record.remote_ip}`}>
                        <span>{ipValue}</span>
                    </Tooltip>
                ) : (
                    <span>{ipValue}</span>
                );

                return isInternalIP(ipValue) ? (
                    ipContent
                ) : (
                    <a
                        href={`https://ip.sb/ip/${ipValue}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{color: '#1890ff'}}
                    >
                        {ipContent}
                    </a>
                );
            },
            hideInTable: false,
            search: true,
        },
        {
            title: t('logsTable.description'),
            width: '15%',
            dataIndex: 'content',
            search: false,
            ellipsis: true,
            render: (text, record) => {
                // 管理员版本的特殊处理：管理员始终可以查看详细信息
                return renderEnhancedDescription(record, isAdmin(userState));
            },
        },
        {
            title: t('logsTable.action'),
            width: '4.5%',
            align: 'center',
            key: 'action-detail',
            search: false,
            render: (_, record) => {
                return <Button type='link'
                               onClick={() => handelOpenLogDetailDrawer(record.id ?? 0)}>{t('logsTable.details')}</Button>;
            },
            hideInSearch: true,
            hideInTable: userState.user.role !== 100 && !(userState.user.role === 10 && hasPermission(userState.user.admin_access_flags, Permission.LogDetail))
        },
        {
            title: t('logsTable.tokenKey'),
            dataIndex: 'token_key',
            hideInTable: true
        },
        {
            title: t('logsTable.requestId'),
            dataIndex: 'request_id',
            hideInTable: true
        },
        {
            title: t('logsTable.requestDuration'),
            dataIndex: 'request_duration',
            hideInTable: true,
            valueType: 'digitRange',
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        request_duration_min: value[0],
                        request_duration_max: value[1]
                    };
                }
            },
        },
        {
            title: t('logsTable.firstByteDuration'),
            dataIndex: 'response_first_byte_duration',
            hideInTable: true,
            valueType: 'digitRange',
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        response_first_byte_duration_min: value[0],
                        response_first_byte_duration_max: value[1]
                    };
                }
            },
        },
        {
            title: t('logsTable.totalDuration'),
            dataIndex: 'total_duration',
            hideInTable: true,
            valueType: 'digitRange',
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        total_duration_min: value[0],
                        total_duration_max: value[1]
                    };
                }
            },
        },
        {
            title: t('logsTable.excludeModels'),
            dataIndex: 'exclude_models',
            hideInTable: true,
            renderFormItem: (_, {type, defaultRender, formItemProps, fieldProps, ...rest}, form) => {
                if (type === 'form') {
                    return null;
                }
                return (
                    <Select
                        mode="tags"
                        style={{width: '100%'}}
                        options={AUTOCOMPLETE_MODEL_NAMES.map(name => ({value: name, label: name}))}
                        {...fieldProps}
                        placeholder='输入/选择模型名称'
                        allowClear
                        showSearch
                        filterOption={(input, option) => {
                            if (typeof option?.label === 'string') {
                                return option.label.toLowerCase().includes(input.toLowerCase());
                            }
                            return false;
                        }}
                    />
                );
            },
        },
        {
            title: t('logsTable.errorCode'),
            dataIndex: 'error_code',
            hideInTable: true,
            renderFormItem: (_item, {type, defaultRender, formItemProps, fieldProps, ...rest}, form) => {
                return (
                    <AutoComplete
                        options={AUTOCOMPLETE_ERROR_CODES.map(code => ({
                            value: code,
                            label: (
                                <div style={{
                                    whiteSpace: 'nowrap',
                                    overflow: 'visible',
                                    textOverflow: 'clip',
                                    width: '100%'
                                }}>
                                    {code}
                                </div>
                            )
                        }))}
                        {...fieldProps}
                        placeholder={t('logsTable.errorCodePlaceholder')}
                        allowClear
                        style={{width: '100%'}}
                        dropdownStyle={{
                            minWidth: '400px',  // 增加最小宽度
                            maxWidth: '600px'   // 增加最大宽度
                        }}
                    />
                );
            }
        },
        {
            title: t('logsTable.excludeErrorCodes'),
            dataIndex: 'exclude_error_codes',
            hideInTable: true,
            renderFormItem: (_, {type, defaultRender, formItemProps, fieldProps, ...rest}, form) => {
                if (type === 'form') {
                    return null;
                }
                return (
                    <Select
                        mode="tags"
                        style={{width: '100%'}}
                        options={AUTOCOMPLETE_ERROR_CODES.map(code => ({value: code, label: code}))}
                        {...fieldProps}
                        placeholder={t('logsTable.excludeErrorCodesPlaceholder')}
                        allowClear
                        showSearch
                        filterOption={(input, option) => {
                            if (typeof option?.label === 'string') {
                                return option.label.toLowerCase().includes(input.toLowerCase());
                            }
                            return false;
                        }}
                        dropdownStyle={{
                            minWidth: '400px',  // 增加最小宽度
                            maxWidth: '600px'   // 增加最大宽度
                        }}
                    />
                );
            },
        },
    ];

    const columns_user: ProColumns<any, string>[] = [
        {
            title: t('logsTable.time'),
            width: '15%',
            dataIndex: 'created_time',
            filters: true,
            order: 0,
            hideInSearch: true,
            valueType: 'date',
            render: (_, record) => {
                let date = moment(record.created_at * 1000);
                return (
                    <Tooltip title={t('message.clipboard.clickToCopy')}>
                        <span style={{cursor: 'pointer'}} onClick={() => {
                            navigator.clipboard.writeText(date.format('YYYY/MM/DD HH:mm:ss'));
                            AntdMessage.success(t('message.clipboard.copySuccess'));
                        }}>
                            {timestamp2string(record.created_at)}
                        </span>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.time'),
            dataIndex: 'created_time',
            valueType: 'dateTimeRange',
            hideInTable: true,
            colSize: 2,
            search: {
                transform: (value) => {
                    if (!value) return {};
                    return {
                        start_timestamp: new Date(value[0]).getTime() / 1000,
                        end_timestamp: new Date(value[1]).getTime() / 1000
                    };
                }
            },
            fieldProps: {
                ranges: {
                    [t('logsTable.today')]: [moment().startOf('day'), moment().endOf('day')],
                    [t('logsTable.lastHour')]: [moment().subtract(1, 'hours'), moment()],
                    [t('logsTable.last3Hours')]: [moment().subtract(3, 'hours'), moment()],
                    [t('logsTable.lastDay')]: [moment().subtract(1, 'days'), moment()],
                    [t('logsTable.last3Days')]: [moment().subtract(3, 'days'), moment()],
                    [t('logsTable.last7Days')]: [moment().subtract(7, 'days'), moment()],
                    [t('logsTable.lastMonth')]: [moment().subtract(1, 'months'), moment()],
                    [t('logsTable.last3Months')]: [moment().subtract(3, 'months'), moment()]
                }
            }
        },
        {
            title: t('logsTable.type'),
            dataIndex: 'type',
            width: '6%',
            fieldProps: {mode: 'multiple'},
            valueEnum: {
                1: {text: t('logsTable.typeRecharge')},
                2: {text: t('logsTable.typeConsumption')},
                3: {text: t('logsTable.typeManagement')},
                4: {text: t('logsTable.typeSystem')},
                5: {text: t('logsTable.typeInvitation')},
                9: {text: t('logsTable.typeCheckin')},
                10: {text: t('logsTable.typeLog')}
            },
            render: (_, record) => {
                const status = LOGS_TYPE[record.type];
                return <Tag color={status.color}> {t(`logsTable.type${status.text}`)} </Tag>;
            }
        },
        {
            title: t('logsTable.tokenName'),
            width: '17.5%',
            dataIndex: 'token_name',
            render: (token_name) => {
                if (!token_name) return '';
                const displayName = typeof token_name === 'string' ? token_name : String(token_name);
                return (
                    <Tooltip title={`${displayName} (${t('message.clipboard.clickToCopy')})`}>
                        <Tag
                            icon={<LockFilled/>}
                            color='cyan'
                            bordered={false}
                            style={{cursor: 'pointer'}}
                            onClick={() => {
                                navigator.clipboard.writeText(displayName);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                        >
                            {displayName}
                        </Tag>
                    </Tooltip>
                );
            }
        },
        {
            title: t('logsTable.tokenGroup'),
            width: '17.5%',
            dataIndex: 'token_group',
            render: (token_group, record) => {
                if (!token_group) return '';

                if (groups.length === 0) {
                    return (
                        <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                            <span>{token_group}</span>
                            {loading && <LoadingOutlined style={{fontSize: 14}}/>}
                        </div>
                    );
                }

                const group = groups.find(g => g.name === token_group);
                if (!group) return token_group;
                const isCurrentGroup = group.name === userState.user.group;
                const ratio = isCurrentGroup ? 
                    group.current_group_ratio : 
                    (group.convert_ratio || 1) * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);

                // 获取最大小数位数
                const maxDecimalPlaces = Math.max(
                    getNumberDecimalPlaces(group.convert_ratio || 1),
                    getNumberDecimalPlaces(group.current_group_ratio || 1),
                    getNumberDecimalPlaces(group.current_topup_ratio || 1)
                );

                // 使用动态精度
                const formattedRatio = ratio.toFixed(maxDecimalPlaces);

                return (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line'}}>
                            <p><strong>分组名称:</strong> {group.display_name}</p>
                            <p><strong>分组标识:</strong> {group.name}</p>
                            <p><strong>转换倍率:</strong> {group.convert_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>分组倍率:</strong> {group.current_group_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>充值倍率:</strong> {group.current_topup_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>最终倍率:</strong> {formattedRatio}x</p>
                        </div>
                    }>
                        <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                            gap: '8px'
                        }}>
                            <span style={{
                                flex: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                minWidth: 0
                            }}>
                                {group.display_name || token_group}
                            </span>
                            <span style={{
                                padding: '2px 8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                backgroundColor: Number(formattedRatio) > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                color: Number(formattedRatio) > 1 ? '#ff4d4f' : '#52c41a',
                                flexShrink: 0
                            }}>
                                {formattedRatio}x
                            </span>
                        </div>
                    </Tooltip>
                );
            },
            renderFormItem: (_item, {defaultRender, ...rest}) => {
                return <Select
                    {...rest}
                    placeholder={t('logsTable.selectGroup')}
                    style={{width: '100%'}}
                    allowClear={true}
                    options={groups.map(group => {
                        const isCurrentGroup = group.name === userState.user.group;
                        const ratio = isCurrentGroup ? 
                            group.current_group_ratio : 
                            (group.convert_ratio || 1) * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);
                        // 获取最大小数位数
                        const maxDecimalPlaces = Math.max(
                            getNumberDecimalPlaces(group.convert_ratio || 1),
                            getNumberDecimalPlaces(group.current_group_ratio || 1),
                            getNumberDecimalPlaces(group.current_topup_ratio || 1)
                        );
                        // 使用动态精度
                        const formattedRatio = ratio.toFixed(maxDecimalPlaces);
                        return {
                            label: (
                                <div style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    width: '100%',
                                    gap: '8px',
                                    whiteSpace: 'nowrap',
                                    overflow: 'visible'
                                }}>
                                    <span style={{
                                        flex: 1,
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        minWidth: 0
                                    }}>
                                        {group.display_name}
                                    </span>
                                    <span style={{
                                        padding: '2px 8px',
                                        borderRadius: '4px',
                                        fontSize: '12px',
                                        backgroundColor: Number(formattedRatio) > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                        color: Number(formattedRatio) > 1 ? '#ff4d4f' : '#52c41a',
                                        flexShrink: 0
                                    }}>
                                        {formattedRatio}x
                                    </span>
                                </div>
                            ),
                            value: group.name
                        };
                    })}
                    dropdownStyle={{
                        minWidth: '300px',
                        maxWidth: '500px'
                    }}
                />;
            }
        },
        {
            title: t('logsTable.model'),
            width: '17.5%',
            dataIndex: 'model_name',
            render: (model_name) => {
                if (!model_name) return '';
                return (
                    <Tooltip title={`${model_name} (${t('message.clipboard.clickToCopy')})`}>
                        <div
                            onClick={() => {
                                navigator.clipboard.writeText(model_name as string);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{cursor: 'pointer'}}
                        >
                            {renderModelTags(model_name as string)}
                        </div>
                    </Tooltip>
                );
            },
            renderFormItem: (_item, {defaultRender, ...rest}) => {
                return <AutoComplete
                    options={AUTOCOMPLETE_MODEL_NAMES.map(name => ({value: name}))}
                    {...rest}
                    placeholder={t('logsTable.modelPlaceholder')}
                    filterOption={(inputValue, option) => {
                        if (!option?.value) return false;
                        return option.value.toLowerCase().includes(inputValue.toLowerCase());
                    }}
                    showSearch={true}
                    allowClear={true}
                    style={{width: '100%'}}
                />;
            }
        },
        {
            title: t('logsTable.duration'),
            width: '7%',
            dataIndex: 'duration_for_view',
            hideInSearch: true,
            render: (text, record) => {
                if (record.type === 2) {
                    if (record.duration_for_view < 1) {
                        return <Tag icon={<ClockCircleFilled/>} bordered={false}
                                    color='success'>{t('logsTable.lessThanOneSecond')}</Tag>;
                    } else {
                        return <Tag icon={<ClockCircleFilled/>} bordered={false}
                                    color='success'>{text}{t('logsTable.seconds')}</Tag>;
                    }
                } else {
                    return '';
                }
            }
        },
        {
            title: t('logsTable.isStream'),
            width: '7%',
            dataIndex: 'is_stream',
            valueEnum: {
                'true': {text: t('common.yes')},
                'false': {text: t('common.no')}
            },
            render: (_, record) => renderIsStream(record),
        },
        {
            title: t('logsTable.prompt'),
            width: '7%',
            dataIndex: 'prompt_tokens',
            search: false,
            render: (text) => text === 0 ? '' : text
        },
        {
            title: t('logsTable.completion'),
            width: '7%',
            dataIndex: 'completion_tokens',
            search: false,
            render: (text) => text === 0 ? '' : text
        },
        {
            title: t('logsTable.consumption'),
            width: '10%',
            dataIndex: 'quota',
            search: false,
            render: (_, record) => (record.quota === 0 || typeof record.quota === 'undefined' || record.quota === null) ? '' : `$${(record.quota / 500000).toFixed(6)}`
        },
        {
            title: t('logsTable.ip'),
            width: '12%',
            dataIndex: 'ip',
            ellipsis: true,
            render: (ip: any, record: any) => {
                const ipValue = record.ip;
                if (!ipValue || typeof ipValue !== 'string') return '';

                const ipContent = record.remote_ip && record.remote_ip !== ipValue ? (
                    <Tooltip title={`${t('logsTable.remoteIp')}: ${record.remote_ip}`}>
                        <span>{ipValue}</span>
                    </Tooltip>
                ) : (
                    <span>{ipValue}</span>
                );

                return isInternalIP(ipValue) ? (
                    ipContent
                ) : (
                    <a
                        href={`https://ip.sb/ip/${ipValue}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{color: '#1890ff'}}
                    >
                        {ipContent}
                    </a>
                );
            },
        },
        {
            title: t('logsTable.description'),
            width: '15%',
            dataIndex: 'content',
            search: false,
            ellipsis: true,
            render: (text, record) => {
                // 用户版本的特殊处理：如果用户日志查看被禁用且是消费类型，显示默认文本
                if (!statusState.status.UserLogViewEnabled && record.type === 2) {
                    return t('logsTable.modelInvocation');
                }
                
                return renderEnhancedDescription(record, false);
            },
        },
        {
            title: t('logsTable.tokenKey'),
            dataIndex: 'token_key',
            hideInTable: true
        }
    ];

    const items = [
        {
            key: '1',
            label: (
                <div style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                    {t('logsTable.dailyModelUsageStats')}
                    <Tooltip
                        title={
                            <div>
                                <div>{t('logsTable.timezoneNote', {
                                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                                })}</div>
                                <div style={{marginTop: '8px'}}>{t('logsTable.timezoneDescription')}</div>
                                <Button
                                    type="link"
                                    size="small"
                                    onClick={() => navigate('/account/profile')}
                                    style={{padding: '4px 0'}}
                                >
                                    {t('logsTable.goToProfile')} →
                                </Button>
                            </div>
                        }
                    >
                        <InfoCircleOutlined style={{fontSize: '14px', color: '#1677ff'}}/>
                    </Tooltip>
                </div>
            ),
            children: (
                <Row gutter={[24, 24]}>
                    <Col span={isMobile() ? 24 : 14}>
                        <DailyUsageByModelEcharts
                            searchParams={searchParams}
                            startTimestamp={startTime?.unix()}
                            endTimestamp={endTime?.unix()}
                            tokenName={formRef.current?.getFieldValue('token_name')}
                            username={formRef.current?.getFieldValue('username')}
                            modelName={formRef.current?.getFieldValue('model_name')}
                            channel={formRef.current?.getFieldValue('channel')}
                            channelName={formRef.current?.getFieldValue('channel_name')}
                            granularityParam={formRef.current?.getFieldValue('dimension') || finalUsageStatsDefaultTimeUnit} // 使用配置的默认时间单位
                        />
                    </Col>
                    <Col span={isMobile() ? 24 : 10}>
                        <div style={{height: '240px', width: '100%'}}>
                            <Spin
                                tip={t('logsTable.modelUsage')}
                                indicator={<LoadingOutlined style={{fontSize: 24}} spin/>}
                                spinning={loadingModelUsageData}
                            >
                                {modelUsageData && modelUsageData.length > 0 ? (
                                    <MemoizedPie data={modelUsageData}/>
                                ) : (
                                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{marginTop: 65}}/>
                                )}
                            </Spin>
                        </div>
                    </Col>
                </Row>
            )
        }
    ];

    const handleChangeLogDurationType = (value: string[]) => {
        if (value.length === 0) {
            return
        }
        setDurationType(value[0])
    };

    // 添加一个新的函数来单独获取 rpm 数据
    const refreshRpmStats = async () => {
        setLoadingStats(true);
        try {
            const {current, pageSize, ...rest} = lastParams || {} as LogParams;
            
            // 优化时间参数传递，确保使用数字格式
            let queryParams = {...rest};
            if (start_timestamp && !isNaN(Number(start_timestamp))) {
                queryParams.start_timestamp = Number(start_timestamp);
            }
            if (end_timestamp && !isNaN(Number(end_timestamp))) {
                queryParams.end_timestamp = Number(end_timestamp);
            }
            
            // 构建查询字符串
            let query = '';
            Object.keys(queryParams).forEach(key => {
                if (queryParams[key] !== undefined && queryParams[key] !== null) {
                    query += `&${key}=${queryParams[key]}`;
                }
            });
            
            const url = isAdmin(userState) ?
                `/api/log/stat?${query}` :
                `/api/log/self/stat?${query}`;
            const res = await API.get(url);
            const {success, data} = res.data;
            if (success) setStat(data);
        } finally {
            setLoadingStats(false);
        }
    };

    const handleViewTotalQuota = async () => {
        if (loadingTotalQuota) return;

        setLoadingTotalQuota(true);
        const loadingMessage = AntdMessage.loading({
            content: t('logsTable.loadingTotalQuota'),
            duration: 0,
        });

        try {
            // 确保包含时间范围参数
            let queryParams = {...lastParams};
            if (startTime && endTime) {
                queryParams.start_timestamp = startTime.unix();
                queryParams.end_timestamp = endTime.unix();
            }
            
            queryParams.use_redis = false;
            
            const url = isAdmin(userState) ? '/api/log/stat' : '/api/log/self/stat';
            const res = await API.get(url, {
                params: queryParams
            });

            if (res.data.success) {
                const totalStats = res.data.data;
                Modal.info({
                    title: t('logsTable.totalQuotaTitle'),
                    maskClosable: true,
                    content: (
                        <>
                            <p>{t('logsTable.totalQuota', {quota: renderQuota(totalStats.quota)})}</p>
                            <p>{t('logsTable.totalRpm', {rpm: totalStats.rpm})}</p>
                            <p>{t('logsTable.totalTpm', {tpm: totalStats.tpm})}</p>
                            <p>{t('logsTable.totalMpm', {mpm: totalStats.mpm.toFixed(4)})}</p>
                            <p>{t('logsTable.dailyEstimate', {estimate: renderQuota(totalStats.mpm * 60 * 24 * 500000)})}</p>
                        </>
                    ) as ReactNode,
                });
            } else {
                AntdMessage.error(t('logsTable.loadTotalQuotaError'));
            }
        } catch (error) {
            AntdMessage.error(t('logsTable.loadTotalQuotaError'));
        } finally {
            loadingMessage();
            setLoadingTotalQuota(false);
        }
    };

    // 修改获取请求日志的函数
    const fetchRequestLogs = async (requestId: string) => {
        setLoadingRequestLogs(true);
        try {
            let url: string;
            if (isAdmin(userState)) {
                url = `/api/log/?p=0&pageSize=100&request_id=${requestId}`;
            } else {
                url = `/api/log/self/?p=0&pageSize=100&request_id=${requestId}`;
            }
            const res = await API.get(url);
            const {success, data} = res.data;
            if (success) {
                setRequestLogs(data);
            } else {
                showError(t('logsTable.failedToLoadRequestLogs'));
            }
        } catch (error) {
            showError(error);
        } finally {
            setLoadingRequestLogs(false);
        }
    };

    // 修改处理点击请求ID的函数
    const handleRequestIdClick = (requestId: string) => {
        // 只有管理员可以查看请求日志详情
        if (!isAdmin(userState)) {
            return;
        }
        setCurrentRequestId(requestId);
        setIsRequestLogsModalVisible(true);
        fetchRequestLogs(requestId);
    };

    // 修改渲染请求ID的部分，只有管理员才显示可点击样式
    const renderRequestId = (requestId: string) => {
        if (!isAdmin(userState)) {
            return requestId;
        }
        return (
            <a onClick={() => handleRequestIdClick(requestId)}>
                {requestId}
            </a>
        );
    };

    // 处理自动刷新间隔变化
    const handleChangeAutoRefresh = (interval: number) => {
        setAutoRefreshInterval(interval);
        if (refreshTimer) {
            clearInterval(refreshTimer);
            setRefreshTimer(null);
        }
        if (interval > 0) {
            const timer = setInterval(refreshRpmStats, interval * 1000);
            setRefreshTimer(timer);
        }
    };

    // 清理定时器
    useEffect(() => {
        return () => {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
        };
    }, [refreshTimer]);

    return (
        <>
            <Collapse
                style={{marginBottom: 15}}
                defaultActiveKey={[(customShowDailyModelUsageStats) ? '1' : '']}
                onChange={async () => {
                    if (customShowDailyModelUsageStats) {
                        setCustomShowDailyModelUsageStats(false);
                        localStorage.setItem('customShowDailyModelUsageStats', 'false');
                    } else {
                        setCustomShowDailyModelUsageStats(true);
                        localStorage.setItem('customShowDailyModelUsageStats', 'true');
                        await fetchStatsData(lastParams, true);
                    }
                }}
                items={items}
            />
            <ProTable
                defaultSize={"small"}
                formRef={formRef}
                scroll={isAdmin(userState) ? {x: 1600} : {x: 950}}
                columnEmptyText={false}
                columns={isAdmin(userState) ? columns_admin as any : columns_user as any}
                request={request as any}
                rowKey='id'
                cardBordered
                // 添加列状态映射和状态变化处理
                columnsStateMap={columnsStateMap}
                onColumnsStateChange={handleColumnsStateChange}
                onSubmit={(values) => {
                    setSearchParams(values);
                }}
                onReset={() => {
                    forceResetFields();
                    setSearchParams(new URLSearchParams());
                    if (startTime && endTime) {
                        formRef.current?.setFieldsValue({
                            created_time: [startTime, endTime]
                        });
                    }
                    setTimeout(() => {
                        formRef.current?.submit();
                    }, 0);
                }}
                search={{labelWidth: 'auto', span: isMobile() ? undefined : 4}}
                form={(startTime !== undefined && endTime !== undefined) ? {
                    initialValues: {
                        created_time: [startTime, endTime]
                    },
                    syncToUrl: (values, type) => {
                        if (type === 'get') {
                            return {...values};
                        }
                        return values;
                    }
                } : {
                    syncToUrl: (values, type) => {
                        if (type === 'get') {
                            return {...values};
                        }
                        return values;
                    }
                }}
                toolbar={needGetSata ?
                    {
                        title: (
                            <div style={{ 
                                display: 'flex', 
                                flexDirection: isMobile() ? 'column' : 'row',
                                alignItems: isMobile() ? 'stretch' : 'center',
                                width: '100%',
                                gap: isMobile() ? '8px' : '0'
                            }}>
                                {/* 数据统计区域 */}
                                <div style={{
                                    flex: 1,
                                    minWidth: 0,
                                    width: '100%'
                                }}>
                                    <ConsumptionMetricsPanel
                                        isLoading={loadingStats}
                                        isMobile={isMobile()}
                                        totalConsumption={renderQuota(stat.quota)}
                                        metrics={{
                                            rpm: stat.rpm || 0,
                                            tpm: stat.tpm || 0,
                                            mpm: stat.mpm || 0,
                                            dailyEstimate: stat.mpm * 60 * 24
                                        }}
                                        onRefresh={refreshRpmStats}
                                        onViewTotal={handleViewTotalQuota}
                                        isLoadingTotal={loadingTotalQuota}
                                        isRealtime={stat.is_realtime_data}
                                        autoRefreshInterval={autoRefreshInterval}
                                        onChangeAutoRefresh={handleChangeAutoRefresh}
                                        t={t}
                                    />
                                </div>
                                
                                {/* 移动端操作按钮区域 */}
                                {isMobile() && (
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '6px',
                                        flexWrap: 'wrap',
                                        justifyContent: 'space-between',
                                        width: '100%'
                                    }}>
                                        {/* 表格模式切换 */}
                                        {setUseResizableTable && (
                                            <div style={{ 
                                                display: 'flex', 
                                                alignItems: 'center', 
                                                gap: '4px',
                                                flexShrink: 0
                                            }}>
                                                <span style={{ 
                                                    fontSize: '12px', 
                                                    color: '#666', 
                                                    fontWeight: 500,
                                                    whiteSpace: 'nowrap'
                                                }}>
                                                    模式:
                                                </span>
                                                <Switch 
                                                    checked={useResizableTable}
                                                    onChange={setUseResizableTable}
                                                    checkedChildren="拖"
                                                    unCheckedChildren="标"
                                                    size="small"
                                                    style={{ 
                                                        minWidth: '45px',
                                                        fontSize: '10px'
                                                    }}
                                                />
                                                <Tag 
                                                    color="orange" 
                                                    style={{ 
                                                        margin: 0, 
                                                        fontSize: '8px', 
                                                        padding: '0 3px', 
                                                        lineHeight: '12px',
                                                        height: '14px',
                                                        display: 'flex',
                                                        alignItems: 'center'
                                                    }}
                                                >
                                                    Beta
                                                </Tag>
                                            </div>
                                        )}

                                        {/* 用户设置 */}
                                        <div style={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            gap: '4px',
                                            flexShrink: 0
                                        }}>
                                            <Switch 
                                                checkedChildren="当日"
                                                unCheckedChildren="全部" 
                                                checked={userOnlyToday}
                                                onChange={(checked) => {
                                                    setUserOnlyToday(checked);
                                                    AntdMessage.success(`已设置默认查询范围为${checked ? '当日' : '全部'}，下次访问将自动应用此设置`);
                                                }}
                                                size="small"
                                            />
                                            
                                            <Select 
                                                value={userTimeUnit}
                                                onChange={(value) => {
                                                    setUserTimeUnit(value);
                                                    AntdMessage.success(`已设置默认时间单位为${value === 'hour' ? '小时' : value === 'day' ? '天' : value === 'week' ? '周' : '月'}，下次访问将自动应用此设置`);
                                                }}
                                                style={{ width: 55, height: 26 }}
                                                size="small"
                                                options={[
                                                    { value: 'hour', label: '时' },
                                                    { value: 'day', label: '天' },
                                                    { value: 'week', label: '周' },
                                                    { value: 'month', label: '月' }
                                                ]}
                                                dropdownMatchSelectWidth={false}
                                            />
                                        </div>

                                        {/* 时长类型选择 */}
                                        {isAdmin(userState) && (
                                            <Select
                                                defaultValue={["2"]}
                                                onChange={handleChangeLogDurationType}
                                                style={{ width: 70, height: 26 }}
                                                size="small"
                                                placeholder="时长"
                                            >
                                                <Select.Option value="1">请求</Select.Option>
                                                <Select.Option value="2">首字节</Select.Option>
                                                <Select.Option value="3">总计</Select.Option>
                                                <Select.Option value="0">全部</Select.Option>
                                            </Select>
                                        )}

                                        {/* 导出按钮 */}
                                        <Popconfirm title={t('logsTable.exportConfirm')} onConfirm={downloadCSV}>
                                            <Button 
                                                icon={<FileExcelFilled/>}
                                                size="small"
                                                style={{ minWidth: '55px' }}
                                            >
                                                导出
                                            </Button>
                                        </Popconfirm>
                                    </div>
                                )}
                            </div>
                        ),
                        actions: isMobile() ? [] : [
                            <div 
                                key="toolbar-actions" 
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    flexWrap: 'nowrap',
                                    minWidth: 0,
                                    maxWidth: '600px',
                                    flexShrink: 0
                                }}
                            >
                                {/* 表格模式切换 - 桌面端 */}
                                {setUseResizableTable && (
                                    <div style={{ 
                                        display: 'flex', 
                                        alignItems: 'center', 
                                        gap: '4px',
                                        flexShrink: 0
                                    }}>
                                        <span style={{ 
                                            fontSize: '13px', 
                                            color: '#666', 
                                            fontWeight: 500,
                                            whiteSpace: 'nowrap'
                                        }}>
                                            表格模式:
                                        </span>
                                        <Tooltip 
                                            title={
                                                useResizableTable 
                                                    ? "使用支持拖拽调整列宽的表格，测试功能可能存在一些限制" 
                                                    : "使用标准ProTable，功能完整稳定"
                                            }
                                        >
                                            <Switch 
                                                checked={useResizableTable}
                                                onChange={setUseResizableTable}
                                                checkedChildren="拖拽"
                                                unCheckedChildren="标准"
                                                style={{ 
                                                    minWidth: '60px',
                                                    fontSize: '11px'
                                                }}
                                            />
                                        </Tooltip>
                                        <Tag 
                                            color="orange" 
                                            style={{ 
                                                margin: 0, 
                                                fontSize: '9px', 
                                                padding: '0 4px', 
                                                lineHeight: '14px',
                                                height: '16px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                fontWeight: 500
                                            }}
                                        >
                                            Beta
                                        </Tag>
                                    </div>
                                )}

                                {/* 用户设置 - 桌面端 */}
                                <div style={{ 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    gap: '4px',
                                    flexShrink: 0
                                }}>
                                    <Tooltip title="设置默认查询范围，下次访问自动应用">
                                        <Switch 
                                            checkedChildren="当日"
                                            unCheckedChildren="全部" 
                                            checked={userOnlyToday}
                                            onChange={(checked) => {
                                                setUserOnlyToday(checked);
                                                AntdMessage.success(`已设置默认查询范围为${checked ? '当日' : '全部'}，下次访问将自动应用此设置`);
                                            }}
                                        />
                                    </Tooltip>
                                    
                                    <Tooltip title="设置默认时间单位，下次访问自动应用">
                                        <Select 
                                            value={userTimeUnit}
                                            onChange={(value) => {
                                                setUserTimeUnit(value);
                                                AntdMessage.success(`已设置默认时间单位为${value === 'hour' ? '小时' : value === 'day' ? '天' : value === 'week' ? '周' : '月'}，下次访问将自动应用此设置`);
                                            }}
                                            style={{ width: 70, height: 32 }}
                                            options={[
                                                { value: 'hour', label: '小时' },
                                                { value: 'day', label: '天' },
                                                { value: 'week', label: '周' },
                                                { value: 'month', label: '月' }
                                            ]}
                                            dropdownMatchSelectWidth={false}
                                        />
                                    </Tooltip>
                                </div>

                                {/* 时长类型选择 - 桌面端 */}
                                {isAdmin(userState) && (
                                    <Select
                                        defaultValue={["2"]}
                                        onChange={handleChangeLogDurationType}
                                        style={{ minWidth: '100px', maxWidth: '120px' }}
                                        placeholder="时长类型"
                                    >
                                        <Select.Option value="1">{t('logsTable.requestDuration')}</Select.Option>
                                        <Select.Option value="2">{t('logsTable.firstByteDuration')}</Select.Option>
                                        <Select.Option value="3">{t('logsTable.totalDuration')}</Select.Option>
                                        <Select.Option value="0">{t('logsTable.showAll')}</Select.Option>
                                    </Select>
                                )}

                                {/* 导出按钮 - 桌面端 */}
                                <Popconfirm title={t('logsTable.exportConfirm')} onConfirm={downloadCSV}>
                                    <Button 
                                        icon={<FileExcelFilled/>}
                                        style={{ minWidth: '80px' }}
                                    >
                                        {t('logsTable.export')}
                                    </Button>
                                </Popconfirm>
                            </div>
                        ]
                    } :
                    {
                        title: (
                            <div style={{ 
                                display: 'flex', 
                                flexDirection: isMobile() ? 'column' : 'row',
                                alignItems: isMobile() ? 'stretch' : 'center',
                                width: '100%',
                                gap: isMobile() ? '8px' : '0'
                            }}>
                                <Button
                                    type='text'
                                    onClick={async () => {
                                        setNeedGetSata(true);
                                        isAdmin(userState) ? await getLogStat(lastParams, true) : await getLogSelfStat(lastParams, true);
                                    }}
                                >
                                    {t('logsTable.statsData')}
                                </Button>
                                
                                {/* 移动端操作按钮 */}
                                {isMobile() && (
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '6px',
                                        flexWrap: 'wrap',
                                        justifyContent: 'flex-end'
                                    }}>
                                        <Switch 
                                            checkedChildren="当日"
                                            unCheckedChildren="全部" 
                                            checked={userOnlyToday}
                                            onChange={(checked) => {
                                                setUserOnlyToday(checked);
                                                AntdMessage.success(`已设置默认查询范围为${checked ? '当日' : '全部'}，下次访问将自动应用此设置`);
                                            }}
                                            size="small"
                                        />
                                        
                                        <Select 
                                            value={userTimeUnit}
                                            onChange={(value) => {
                                                setUserTimeUnit(value);
                                                AntdMessage.success(`已设置默认时间单位为${value === 'hour' ? '小时' : value === 'day' ? '天' : value === 'week' ? '周' : '月'}，下次访问将自动应用此设置`);
                                            }}
                                            style={{ width: 55, height: 26 }}
                                            size="small"
                                            options={[
                                                { value: 'hour', label: '时' },
                                                { value: 'day', label: '天' },
                                                { value: 'week', label: '周' },
                                                { value: 'month', label: '月' }
                                            ]}
                                            dropdownMatchSelectWidth={false}
                                        />

                                        <Popconfirm title={t('logsTable.exportConfirm')} onConfirm={downloadCSV}>
                                            <Button 
                                                icon={<FileExcelFilled/>}
                                                size="small"
                                                style={{ minWidth: '55px' }}
                                            >
                                                导出
                                            </Button>
                                        </Popconfirm>
                                    </div>
                                )}
                            </div>
                        ),
                        actions: isMobile() ? [] : [
                            <div 
                                key="simple-toolbar-actions" 
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    flexWrap: 'nowrap',
                                    flexShrink: 0
                                }}
                            >
                                {/* 用户设置 - 桌面端简化版 */}
                                <div style={{ 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    gap: '4px',
                                    flexShrink: 0
                                }}>
                                    <Tooltip title="设置默认查询范围，下次访问自动应用">
                                        <Switch 
                                            checkedChildren="当日"
                                            unCheckedChildren="全部" 
                                            checked={userOnlyToday}
                                            onChange={(checked) => {
                                                setUserOnlyToday(checked);
                                                AntdMessage.success(`已设置默认查询范围为${checked ? '当日' : '全部'}，下次访问将自动应用此设置`);
                                            }}
                                        />
                                    </Tooltip>
                                    
                                    <Tooltip title="设置默认时间单位，下次访问自动应用">
                                        <Select 
                                            value={userTimeUnit}
                                            onChange={(value) => {
                                                setUserTimeUnit(value);
                                                AntdMessage.success(`已设置默认时间单位为${value === 'hour' ? '小时' : value === 'day' ? '天' : value === 'week' ? '周' : '月'}，下次访问将自动应用此设置`);
                                            }}
                                            style={{ width: 70, height: 32 }}
                                            options={[
                                                { value: 'hour', label: '小时' },
                                                { value: 'day', label: '天' },
                                                { value: 'week', label: '周' },
                                                { value: 'month', label: '月' }
                                            ]}
                                            dropdownMatchSelectWidth={false}
                                        />
                                    </Tooltip>
                                </div>

                                {/* 导出按钮 - 桌面端 */}
                                <Popconfirm title={t('logsTable.exportConfirm')} onConfirm={downloadCSV}>
                                    <Button 
                                        icon={<FileExcelFilled/>}
                                        style={{ minWidth: '80px' }}
                                    >
                                        {t('logsTable.export')}
                                    </Button>
                                </Popconfirm>
                            </div>
                        ]
                    }}
                pagination={{...paginationProps, total: logsCount,}}
            />

            {/* 全局日志详情弹窗 */}
            <LogContentDetail
                visible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                content={currentContent}
            />

            {/* 请求日志详情弹窗 */}
            <Modal
                title={t('logsTable.requestLogs', {requestId: currentRequestId})}
                open={isRequestLogsModalVisible}
                onCancel={() => setIsRequestLogsModalVisible(false)}
                width={1200}
                footer={null}
            >
                <Spin spinning={loadingRequestLogs}>
                    {requestLogs.length > 0 ? (
                        <ProTable
                            defaultSize="small"
                            scroll={isAdmin(userState) ? {x: 1600} : {x: 950}}
                            columnEmptyText={false}
                            columns={(isAdmin(userState) ? columns_admin : columns_user)
                                .filter(col => col.key !== 'action-detail') as any}
                            dataSource={requestLogs as any}
                            rowKey="id"
                            pagination={false}
                            search={false}
                            options={false}
                        />
                    ) : (
                        <Empty description={t('logsTable.noRequestLogs')}/>
                    )}
                </Spin>
            </Modal>

            {isAdmin(userState) &&
                <LogDetailDrawer
                    isLogDetailDrawerVisible={isLogDetailDrawerVisible}
                    setIsLogDetailDrawerVisible={setIsLogDetailDrawerVisible}
                    showLogDetailDrawerLogId={showLogDetailDrawerLogId}
                    setShowLogDetailDrawerLogId={setShowLogDetailDrawerLogId}
                />
            }
        </>
    );
};

export default LogsTable;
