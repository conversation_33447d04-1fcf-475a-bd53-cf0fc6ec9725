import React, { useState, useMemo } from "react";
import { App, AutoComplete, Button, Modal, Radio, Space } from "antd";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { xonokai } from "react-syntax-highlighter/dist/esm/styles/prism";
import { HappyProvider } from "@ant-design/happy-work-theme";
import { modalProps } from "../../constants";
import { copy } from "../../helpers";

interface CodeExampleModalProps {
    isCodeExampleModalVisible: boolean;
    setIsCodeExampleModalVisible: (visible: boolean) => void;
    serverAddress: string;
    apiToken: string;
    availableModels?: string[]; // 新增：可用模型列表
}

type LanguageOption = 'Curl' | 'Python (SDK)' | 'Python (requests)' | 'Node.js' | 'Java' | 'C#' | 'Ruby' | 'Go' | 'PHP' | 'Rust' | 'C' | 'C++' | 'Dart' | 'AutoJS';

type ApiFormat = 'chat/completions' | 'responses' | 'messages' | 'images/generations' | 'images/edits';

const CodeExampleModal: React.FC<CodeExampleModalProps> = ({
                                                               isCodeExampleModalVisible,
                                                               setIsCodeExampleModalVisible,
                                                               serverAddress,
                                                               apiToken,
                                                               availableModels = []
                                                           }) => {
    const [selectModel, setSelectModel] = useState<string>('gpt-4o-mini');
    const [isTestMode, setIsTestMode] = useState<boolean>(false);
    const [isStreamMode, setIsStreamMode] = useState<boolean>(false);
    const [codeExampleLanguage, setCodeExampleLanguage] = useState<LanguageOption>('Curl');
    const [apiFormat, setApiFormat] = useState<ApiFormat>('chat/completions');
    const { message: AntdMessage } = App.useApp();

    // 静态模型列表作为备选
    const staticModels = ['gpt-4o-mini', 'gpt-3.5-turbo', 'gpt-4-turbo', 'claude-3-opus-20240229'];

    // 合并动态模型和静态模型，去重并排序
    const modelOptions = useMemo(() => {
        // 如果没有可用模型列表，使用静态列表
        if (!availableModels || availableModels.length === 0) {
            return staticModels.map(model => ({ value: model }));
        }

        // 创建一个Set来快速去重
        const modelSet = new Set<string>();
        const result: string[] = [];

        // 先添加动态模型（按令牌可用性排序）
        availableModels.forEach(model => {
            if (model && !modelSet.has(model)) {
                modelSet.add(model);
                result.push(model);
            }
        });

        // 再添加静态模型作为补充
        staticModels.forEach(model => {
            if (model && !modelSet.has(model)) {
                modelSet.add(model);
                result.push(model);
            }
        });

        return result.map(model => ({ value: model }));
    }, [availableModels]);

    // 静态模型列表作为备选
    const staticModels = ['gpt-4o-mini', 'gpt-3.5-turbo', 'gpt-4-turbo', 'claude-3-opus-20240229'];

    // 合并动态模型和静态模型，去重并排序
    const modelOptions = useMemo(() => {
        // 如果没有可用模型列表，使用静态列表
        if (!availableModels || availableModels.length === 0) {
            return staticModels.map(model => ({ value: model }));
        }

        // 创建一个Set来快速去重
        const modelSet = new Set<string>();
        const result: string[] = [];

        // 先添加动态模型（按令牌可用性排序）
        availableModels.forEach(model => {
            if (model && !modelSet.has(model)) {
                modelSet.add(model);
                result.push(model);
            }
        });

        // 再添加静态模型作为补充
        staticModels.forEach(model => {
            if (model && !modelSet.has(model)) {
                modelSet.add(model);
                result.push(model);
            }
        });

        return result.map(model => ({ value: model }));
    }, [availableModels]);
    const getLanguageForHighlighter = (language: LanguageOption): string => {
        const languageMap: { [key in LanguageOption]: string } = {
            'Curl': 'bash',
            'Python (SDK)': 'python',
            'Python (requests)': 'python',
            'Node.js': 'javascript',
            'Java': 'java',
            'C#': 'csharp',
            'Ruby': 'ruby',
            'Go': 'go',
            'PHP': 'php',
            'Rust': 'rust',
            'C': 'c',
            'C++': 'cpp',
            'Dart': 'dart',
            'AutoJS': 'javascript'
        };
        return languageMap[language] || language.toLowerCase();
    };

    const getApiEndpoint = () => {
        switch (apiFormat) {
            case 'responses':
                return '/v1/responses';
            case 'messages':
                return '/v1/messages';
            case 'images/generations':
                return '/v1/images/generations';
            case 'images/edits':
                return '/v1/images/edits';
            default:
                return '/v1/chat/completions';
        }
    };

    const getRequestBody = () => {
        const testContent = isTestMode ? "say 1" : "Hello!";

        switch (apiFormat) {
            case 'responses':
                return {
                    model: selectModel,
                    input: testContent,
                    instructions: "You are a helpful assistant.",
                    ...(isStreamMode && { stream: true })
                };
            case 'messages':
                return {
                    model: selectModel,
                    max_tokens: 1024,
                    stream: isStreamMode,
                    messages: [
                        { role: "user", content: testContent }
                    ],
                    system: "You are a helpful assistant."
                };
            case 'images/generations':
                return {
                    model: selectModel || "dall-e-3",
                    prompt: isTestMode ? "A simple red circle" : "A cute baby sea otter",
                    n: 1,
                    size: "1024x1024"
                };
            case 'images/edits':
                return {
                    model: selectModel || "dall-e-2",
                    prompt: isTestMode ? "Add a red hat" : "Create a lovely gift basket with these items"
                };
            default:
                return {
                    model: selectModel,
                    stream: isStreamMode,
                    messages: [
                        { role: "system", content: "You are a helpful assistant." },
                        { role: "user", content: testContent }
                    ]
                };
        }
    };

    const codeExamples = useMemo(() => {
        const endpoint = getApiEndpoint();
        const requestBody = getRequestBody();

        return {
        'Curl': `curl ${serverAddress}${endpoint} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  ${apiFormat === 'messages' ? '-H "anthropic-version: 2023-06-01" \\' : ''}
  -d '${JSON.stringify(requestBody, null, 2).replace(/\n/g, '\n  ')}'`,
        'Python (SDK)': `from openai import OpenAI

client = OpenAI(api_key="sk-${apiToken}", base_url="${serverAddress}/v1")

completion = client.chat.completions.create(
    model="${selectModel}",
    stream=${isStreamMode ? 'True' : 'False'},
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
    ]
)

${isStreamMode ?
            `for chunk in completion:
    print(chunk.choices[0].delta)` :
            `print(completion.choices[0].message)`}`,
        'Python (requests)': `import requests
import json

url = "${serverAddress}/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer sk-${apiToken}"
}
data = {
    "model": "${selectModel}",
    "stream": ${isStreamMode ? 'True' : 'False'},
    "messages": [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
    ]
}

response = requests.post(url, headers=headers, json=data)

if response.status_code == 200:
    ${isStreamMode ? 
    `# 处理流式响应
    for line in response.iter_lines():
        if line:
            line_text = line.decode('utf-8')
            if line_text.startswith('data: '):
                data_str = line_text[6:]  # 去掉'data: '前缀
                if data_str.strip() == '[DONE]':
                    break
                try:
                    chunk = json.loads(data_str)
                    if chunk['choices'][0].get('delta', {}).get('content'):
                        print(chunk['choices'][0]['delta']['content'], end='')
                except json.JSONDecodeError:
                    pass` 
    : 
    `# 处理非流式响应
    print(response.json()['choices'][0]['message']['content'])`}
else:
    print(f"Error: {response.status_code}, {response.text}")`,
        'Node.js': `import OpenAI from "openai";

const openai = new OpenAI({
    apiKey: "sk-${apiToken}",
    baseURL: "${serverAddress}/v1"
});

async function main() {
    const completion = await openai.chat.completions.create({
        model: "${selectModel}",
        stream: ${isStreamMode},
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            { role: "user", content: "${isTestMode ? "say 1" : "Hello!"}" }
        ]
    });

    ${isStreamMode ?
            `for await (const chunk of completion) {
        console.log(chunk.choices[0].delta);
    }` :
            `console.log(completion.choices[0].message);`}
}

main();`,
        'Java': `import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.service.OpenAiService;
import com.theokanning.openai.client.OpenAiApi;

import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.time.Duration;
import java.util.Arrays;

public class OpenAIExample {
    public static void main(String[] args) {
        String token = "sk-${apiToken}";
        String baseUrl = "${serverAddress}/v1/";
        
        ObjectMapper mapper = OpenAiService.defaultObjectMapper();
        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(OpenAiService.defaultClient(token, Duration.ofSeconds(30)))
            .addConverterFactory(JacksonConverterFactory.create(mapper))
            .build();
        OpenAiApi api = retrofit.create(OpenAiApi.class);
        OpenAiService service = new OpenAiService(api);

        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
            .model("${selectModel}")
            .messages(Arrays.asList(
                new ChatMessage("system", "You are a helpful assistant."),
                new ChatMessage("user", "${isTestMode ? "say 1" : "Hello!"}")
            ))
            .build();

        service.createChatCompletion(completionRequest).getChoices().forEach(choice -> {
            System.out.println(choice.getMessage().getContent());
        });
    }
}`,
        'C#': `using OpenAI_API;
using OpenAI_API.Chat;

class Program
{
    static async Task Main(string[] args)
    {
        var api = new OpenAIAPI("sk-${apiToken}");
        api.ApiBase = "${serverAddress}/v1";

        var chat = api.Chat.CreateConversation();
        chat.AppendSystemMessage("You are a helpful assistant.");
        chat.AppendUserInput("${isTestMode ? "say 1" : "Hello!"}");

        string response = await chat.GetResponseFromChatbotAsync();
        Console.WriteLine(response);
    }
}`,
        'Ruby': `require 'openai'

client = OpenAI::Client.new(access_token: 'sk-${apiToken}', uri_base: '${serverAddress}/v1')

response = client.chat(
    parameters: {
        model: "${selectModel}",
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            { role: "user", content: "${isTestMode ? "say 1" : "Hello!"}" }
        ],
        stream: ${isStreamMode}
    }
)

${isStreamMode ?
            `response.each do |chunk|
  print chunk.dig("choices", 0, "delta", "content")
end` :
            `puts response.dig("choices", 0, "message", "content")`}`,
        'Go': `package main

import (
    "context"
    "fmt"
    "github.com/sashabaranov/go-openai"
)

func main() {
    client := openai.NewClient("sk-${apiToken}")
    client.BaseURL = "${serverAddress}/v1"

    resp, err := client.CreateChatCompletion(
        context.Background(),
        openai.ChatCompletionRequest{
            Model: "${selectModel}",
            Messages: []openai.ChatCompletionMessage{
                {
                    Role:    openai.ChatMessageRoleSystem,
                    Content: "You are a helpful assistant.",
                },
                {
                    Role:    openai.ChatMessageRoleUser,
                    Content: "${isTestMode ? "say 1" : "Hello!"}",
                },
            },
        },
    )

    if err != nil {
        fmt.Printf("ChatCompletion error: %v\n", err)
        return
    }

    fmt.Println(resp.Choices[0].Message.Content)
}`,
        'PHP': `<?php

require_once 'vendor/autoload.php';

$client = OpenAI::client('sk-${apiToken}');

$result = $client->chat()->create([
    'model' => '${selectModel}',
    'messages' => [
        ['role' => 'system', 'content' => 'You are a helpful assistant.'],
        ['role' => 'user', 'content' => '${isTestMode ? "say 1" : "Hello!"}'],
    ],
]);

echo $result->choices[0]->message->content;`,
        'Rust': `use reqwest::Client;
use serde_json::{json, Value};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let client = Client::new();
    let response = client.post("${serverAddress}/v1/chat/completions")
        .header("Authorization", "Bearer sk-${apiToken}")
        .json(&json!({
            "model": "${selectModel}",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
            ]
        }))
        .send()
        .await?;

    let body: Value = response.json().await?;
    println!("{}", body["choices"][0]["message"]["content"]);

    Ok(())
}`,
        'C': `#include <stdio.h>
#include <curl/curl.h>
#include <string.h>

size_t write_callback(char *ptr, size_t size, size_t nmemb, void *userdata) {
    printf("%.*s", (int)(size * nmemb), ptr);
    return size * nmemb;
}

int main(void) {
    CURL *curl;
    CURLcode res;
    struct curl_slist *headers = NULL;
    char *data = "{"
        "\"model\": \"${selectModel}\","
        "\"messages\": ["
            "{\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},"
            "{\"role\": \"user\", \"content\": \"${isTestMode ? "say 1" : "Hello!"}\"}"
        "]"
    "}";

    curl = curl_easy_init();
    if(curl) {
        headers = curl_slist_append(headers, "Content-Type: application/json");
        headers = curl_slist_append(headers, "Authorization: Bearer sk-${apiToken}");

        curl_easy_setopt(curl, CURLOPT_URL, "${serverAddress}/v1/chat/completions");
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);

        res = curl_easy_perform(curl);
        if(res != CURLE_OK)
            fprintf(stderr, "curl_easy_perform() failed: %s\\n", curl_easy_strerror(res));

        curl_easy_cleanup(curl);
        curl_slist_free_all(headers);
    }
    return 0;
}`,
        'C++': `#include <iostream>
#include <curl/curl.h>
#include <string>

size_t WriteCallback(void *contents, size_t size, size_t nmemb, std::string *s) {
    size_t newLength = size * nmemb;
    s->append((char*)contents, newLength);
    return newLength;
}

int main() {
    CURL *curl;
    CURLcode res;
    std::string readBuffer;

    curl = curl_easy_init();
    if(curl) {
        std::string url = "${serverAddress}/v1/chat/completions";
        std::string data = R"({
            "model": "${selectModel}",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
            ]
        })";

        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        headers = curl_slist_append(headers, "Authorization: Bearer sk-${apiToken}");

        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);

        res = curl_easy_perform(curl);
        curl_easy_cleanup(curl);

        if(res != CURLE_OK)
            std::cerr << "curl_easy_perform() failed: " << curl_easy_strerror(res) << std::endl;
        else
            std::cout << readBuffer << std::endl;
    }

    return 0;
}`,
        'Dart': `import 'package:http/http.dart' as http;
import 'dart:convert';

void main() async {
  final url = Uri.parse('${serverAddress}/v1/chat/completions');
  final response = await http.post(
    url,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer sk-${apiToken}',
    },
    body: jsonEncode({
      'model': '${selectModel}',
      'messages': [
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'user', 'content': '${isTestMode ? "say 1" : "Hello!"}'},
      ],
    }),
  );

  if (response.statusCode == 200) {
    final jsonResponse = jsonDecode(response.body);
    print(jsonResponse['choices'][0]['message']['content']);
  } else {
    print('Request failed with status: \${response.statusCode}.');
  }
}`,
        'AutoJS': `// AutoJS 示例代码
// 需要先安装 http 模块
let http = require('http');

// 构建请求数据
let requestData = {
    "model": "${selectModel}",
    "stream": ${isStreamMode},
    "messages": [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
    ]
};

// 设置请求头
let headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer sk-${apiToken}"
};

// 发送 POST 请求
let response = http.postJson("${serverAddress}/v1/chat/completions", requestData, {
    headers: headers
});

// 处理响应
if (response.statusCode == 200) {
    let result = response.body.json();
    ${isStreamMode ?
    `// 流式响应处理
    console.log("流式响应暂不支持，请使用非流式模式");` :
    `// 非流式响应处理
    if (result.choices && result.choices.length > 0) {
        console.log("AI回复:", result.choices[0].message.content);
        toast("AI回复: " + result.choices[0].message.content);
    } else {
        console.log("未获取到有效回复");
    }`}
} else {
    console.log("请求失败，状态码:", response.statusCode);
    console.log("错误信息:", response.body.string());
    toast("请求失败: " + response.statusCode);
}`
        };
    }, [serverAddress, apiToken, selectModel, isStreamMode, isTestMode, apiFormat]);

    return (
        <Modal
            {...modalProps}
            title="代码示例"
            open={isCodeExampleModalVisible}
            onOk={() => setIsCodeExampleModalVisible(false)}
            onCancel={() => setIsCodeExampleModalVisible(false)}
            width={840}
            centered={true}
            afterClose={() => {
                setCodeExampleLanguage('Curl');
                setIsTestMode(false);
                setIsStreamMode(false);
                setSelectModel('gpt-4o-mini');
                setApiFormat('chat/completions');
            }}
        >
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                <Space wrap>
                    <span style={{ marginRight: 8, fontWeight: 'bold' }}>API格式:</span>
                    <Radio.Group
                        value={apiFormat}
                        onChange={(e) => setApiFormat(e.target.value)}
                        style={{ marginRight: 16 }}
                    >
                        <Radio.Button value="chat/completions">OpenAI Chat</Radio.Button>
                        <Radio.Button value="responses">OpenAI Responses</Radio.Button>
                        <Radio.Button value="messages">Anthropic Messages</Radio.Button>
                        <Radio.Button value="images/generations">OpenAI Images</Radio.Button>
                        <Radio.Button value="images/edits">OpenAI Image Edit</Radio.Button>
                    </Radio.Group>
                </Space>
                <Space wrap>
                    <span style={{ marginRight: 8, fontWeight: 'bold' }}>编程语言:</span>
                    <Radio.Group
                        value={codeExampleLanguage}
                        onChange={(e) => setCodeExampleLanguage(e.target.value)}
                    >
                        <Radio.Button value="Curl">Curl</Radio.Button>
                        <Radio.Button value="Python (SDK)">Python (SDK)</Radio.Button>
                        <Radio.Button value="Python (requests)">Python (requests)</Radio.Button>
                        <Radio.Button value="Node.js">Node.js</Radio.Button>
                        <Radio.Button value="Java">Java</Radio.Button>
                        <Radio.Button value="C#">C#</Radio.Button>
                        <Radio.Button value="Ruby">Ruby</Radio.Button>
                        <Radio.Button value="Go">Go</Radio.Button>
                        <Radio.Button value="PHP">PHP</Radio.Button>
                        <Radio.Button value="Rust">Rust</Radio.Button>
                        <Radio.Button value="C">C</Radio.Button>
                        <Radio.Button value="C++">C++</Radio.Button>
                        <Radio.Button value="Dart">Dart</Radio.Button>
                        <Radio.Button value="AutoJS">AutoJS</Radio.Button>
                    </Radio.Group>
                    <Radio.Group
                        value={isTestMode}
                        onChange={(e) => setIsTestMode(e.target.value)}
                    >
                        <Radio.Button value={false}>正常</Radio.Button>
                        <Radio.Button value={true}>拨测</Radio.Button>
                    </Radio.Group>
                    <Radio.Group
                        value={isStreamMode}
                        onChange={(e) => setIsStreamMode(e.target.value)}
                    >
                        <Radio.Button value={false}>非流</Radio.Button>
                        <Radio.Button value={true}>流式</Radio.Button>
                    </Radio.Group>
                    <AutoComplete
                        value={selectModel}
                        options={modelOptions}
                        style={{width: 200}}
                        onSelect={(value: string) => setSelectModel(value)}
                        onChange={(value: string) => setSelectModel(value)}
                        placeholder="选择或输入模型名称"
                        filterOption={(inputValue, option) => {
                            if (!option?.value) return false;
                            const optionValue = String(option.value).toLowerCase();
                            const input = inputValue.toLowerCase();
                            return optionValue.includes(input);
                        }}
                    />
                </Space>
                <SyntaxHighlighter
                    language={getLanguageForHighlighter(codeExampleLanguage)}
                    style={xonokai}
                    wrapLines={true}
                    wrapLongLines={true}
                >
                    {codeExamples[codeExampleLanguage]}
                </SyntaxHighlighter>
                <HappyProvider>
                    <Button
                        block={true}
                        type="primary"
                        onClick={async () => {
                            try {
                                await copy(codeExamples[codeExampleLanguage]);
                                AntdMessage.success({content: '复制成功', duration: 1, key: 'copy'});
                            } catch {
                                AntdMessage.warning({content: '复制失败', duration: 1, key: 'copy'});
                            }
                        }}
                    >
                        复制到剪贴板
                    </Button>
                </HappyProvider>
            </Space>
        </Modal>
    );
};

export default CodeExampleModal;
