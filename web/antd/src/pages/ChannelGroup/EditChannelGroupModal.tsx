// @ts-nocheck

import React, {useEffect, useState} from 'react'
import {
    AutoComplete,
    Button,
    Checkbox,
    Col,
    Divider,
    Form,
    Flex,
    Input,
    InputNumber,
    message as AntdMessage,
    Row,
    Select,
    Space,
    Spin,
    Tooltip,
    Transfer
} from 'antd'
import {useNavigate} from 'react-router-dom'
import {API} from '../../helpers'
import {isUrl} from '@ant-design/pro-components'
import {AxiosResponse} from 'axios'

const AddChannelGroupPage = ({channelGroupId, closeModal}) => {
    const isEdit = channelGroupId !== undefined && channelGroupId !== '';
    const originInputs = {
        name: '',//名称
        status: 1,//状态
        group: ['default'],//可用分组
        models: [],//可用模型
        sort: 0,//排序
        weight: 0,//权重
        overFrequencyAutoDisable: true,//超频熔断
        retryInterval: 300,//自动重试
        base_url: '',//代理url
        request_token_limit_enabled: false,//请求令牌限制
        min_request_token_count: 0,//最小请求令牌数量
        max_request_token_count: 0,//最大请求令牌数量
        config: '{"proxy_enabled":false,"proxy_url":"","proxy_auth":""}',//配置
        sync_both_db: false,//双数据库同步，默认关闭
    };
    const [inputs, setInputs] = useState(originInputs);

    const [form] = Form.useForm();
    const [customModel, setCustomModel] = useState('');
    const [inputModels, setInputModels] = useState(''); // 批量添加模型输入
    const [basicModels, setBasicModels] = useState([]);
    const [advancedModels, setAdvancedModels] = useState([]);
    const [allGptModels, setAllGptModels] = useState([]);
    const [groupOptions, setGroupOptions] = useState([]);
    const [originModelOptions, setOriginModelOptions] = useState([]);
    const [modelOptions, setModelOptions] = useState([]);
    const [submitting, setSubmitting] = useState(false);
    const [loading, setLoading] = useState(isEdit)
    const [coverAll, setCoverAll] = useState(true)
    const [prefixToRemove, setPrefixToRemove] = useState(''); // 添加要移除的模型前缀
    const [prefixToKeep, setPrefixToKeep] = useState(''); // 添加要保留的模型前缀
    const allSettings = ["name", "status", "group", "models", "base_url", "sort", "weight", "retryInterval", "overFrequencyAutoDisable", "request_token_limit_enabled", "min_request_token_count", "max_request_token_count", "config.proxy_enabled", "config.proxy_url", "config.proxy_auth"];
    const settingsMapping = {
        "name": "名称",
        "status": "状态",
        "group": "可用分组",
        "models": "可用模型",
        "base_url": "代理地址",
        "sort": "强制排序",
        "weight": "渠道权重",
        "retryInterval": "重试周期",
        "overFrequencyAutoDisable": "超频熔断",
        "request_token_limit_enabled": "请求令牌限制",
        "min_request_token_count": "最小请求令牌数量",
        "max_request_token_count": "最大请求令牌数量",
        "config.proxy_enabled": "启用代理",
        "config.proxy_url": "代理服务器URL",
        "config.proxy_auth": "代理认证信息",
    };
    const [coveredSettings, setCoveredSettings] = useState([]); // 默认覆盖所有设置
    const navigate = useNavigate();
    const {Option} = Select;

    const handleChange = (nextTargetKeys) => {
        setCoveredSettings(nextTargetKeys);
    };

    //获取正在编辑的渠道组的信息
    const fetchChannelGroupData = async () => {
        try {
            let res = await API.get(`/api/channelGroup/${channelGroupId}`);
            const {success, message, data} = res.data;
            if (success) {
                // 解析config字段
                let configObj = {};
                if (data.config) {
                    try {
                        configObj = JSON.parse(data.config);
                    } catch (e) {
                        console.error("解析config字段失败:", e);
                    }
                }
                
                setInputs(data);
                // 将group、models字符串分割成数组
                form.setFieldsValue({
                    ...data,
                    group: data.group.split(','),
                    models: data.models.split(','),
                    sort: data.sort,
                    weight: data.weight,
                    // overFrequencyAutoDisable: data.overFrequencyAutoDisable === 1,
                    overFrequencyAutoDisable: data.overFrequencyAutoDisable,
                    retryInterval: data.retryInterval,
                    request_token_limit_enabled: data.request_token_limit_enabled || false,
                    min_request_token_count: data.min_request_token_count || 0,
                    max_request_token_count: data.max_request_token_count || 0,
                    config: configObj
                });
            } else {
                AntdMessage.error(message);
            }
        } catch (e) {
            console.log(e)
        } finally {
            setLoading(false)
        }
    };

    // 获取配置的用户分组列表
    const fetchGroups = async () => {
        try {
            let res = await API.get(`/api/group/`);
            if (res && res.data && Array.isArray(res.data.data)) {
                setGroupOptions(res.data.data.map((group) => ({
                    label: group, // 显示在下拉菜单中的文本
                    value: group // 实际的值
                })));
            }
        } catch (error) {
            AntdMessage.error(error.message);
        }
    };

    //获取所有可用模型
    const fetchModels = async () => {
        try {
            let res = await API.get(`/api/channel/models`);
            let localModelOptions = res.data.data.map((model) => ({
                key: model.id,
                text: model.id,
                value: model.id
            }));
            setOriginModelOptions(localModelOptions);
            setBasicModels(res.data.data.filter((model) => {
                return model.id.startsWith('gpt-3');
            }).map((model) => model.id));
            setAdvancedModels(res.data.data.filter((model) => {
                return model.id.startsWith('gpt-4') && !model.id.includes('32k');
            }).map((model) => model.id));
            setAllGptModels(res.data.data.filter((model) => {
                return model.id.includes('gpt');
            }).map((model) => model.id));
        } catch (error) {
            AntdMessage.error(error.message);
        }
    };

    //添加自定义模型
    const addCustomModel = () => {
        if (customModel.trim() === '') return;
        let localModels = form.getFieldValue('models') || [];
        if (localModels.includes(customModel)) return;
        localModels.push(customModel);
        let localModelOptions = [];
        localModelOptions.push({
            key: customModel,
            text: customModel,
            value: customModel
        });
        setModelOptions(modelOptions => {
            return [...modelOptions, ...localModelOptions];
        });
        setCustomModel('');
        form.setFieldsValue({models: localModels});
    };

    // 批量添加模型
    const batchAddModels = () => {
        const models = inputModels.trim();
        if (models === '') return;
        
        // 处理模型名称，可能是a,b,c 或 "a","b","c"
        const modelArray = models.split(',').map((item) => item.trim().replace(/"/g, ''));
        const currentModels = form.getFieldValue('models') || [];
        const uniqueModels = [...new Set([...currentModels, ...modelArray])];
        
        form.setFieldsValue({models: uniqueModels});
        setInputModels('');
        AntdMessage.success(`已添加 ${modelArray.length} 个模型`);
    };

    // 移除特定前缀的模型
    const removeModelsByPrefix = () => {
        if (!prefixToRemove) {
            AntdMessage.warning('请输入要移除的模型前缀');
            return;
        }
        const currentModels = form.getFieldValue('models') || [];
        const filteredModels = currentModels.filter(model => !model.startsWith(prefixToRemove));

        if (currentModels.length === filteredModels.length) {
            AntdMessage.info('没有找到匹配的模型');
        } else {
            form.setFieldsValue({models: filteredModels});
            AntdMessage.success(`已移除前缀为 "${prefixToRemove}" 的模型`);
        }
        setPrefixToRemove('');
    };

    // 只保留特定前缀的模型
    const keepModelsByPrefix = () => {
        if (!prefixToKeep) {
            AntdMessage.warning('请输入要保留的模型前缀');
            return;
        }
        const currentModels = form.getFieldValue('models') || [];
        const filteredModels = currentModels.filter(model => model.startsWith(prefixToKeep));

        if (filteredModels.length === 0) {
            AntdMessage.info('没有找到匹配的模型');
        } else {
            form.setFieldsValue({models: filteredModels});
            AntdMessage.success(`已保留前缀为 "${prefixToKeep}" 的模型，共 ${filteredModels.length} 个`);
        }
        setPrefixToKeep('');
    };

    // 在组件加载时获取用户分组
    useEffect(() => {
        fetchGroups();
        fetchModels();
        if (isEdit) {
            fetchChannelGroupData();
        } else {
            form.setFieldsValue(originInputs);//置表单字段为初始值
        }
    }, []);

    useEffect(() => {
        let localModelOptions = [...originModelOptions];
        if (Array.isArray(inputs.models)) {
            inputs.models.forEach((model) => {
                if (!localModelOptions.find((option) => option.key === model)) {
                    localModelOptions.push({
                        key: model,
                        text: model,
                        value: model
                    });
                }
            });
        }
        setModelOptions(localModelOptions);
    }, [originModelOptions, inputs.models]);

    // 监听表单字段
    const requestTokenLimitEnabled = Form.useWatch('request_token_limit_enabled', form);
    const proxyEnabled = Form.useWatch(['config', 'proxy_enabled'], form);

    //提交的操作
    const onFinish = async (values) => {
        const groupsString = values.group.join(','); // 将group数组转换为逗号分隔的字符串
        const modelsString = values.models.join(','); // 将models数组转换为逗号分隔的字符串
        
        // 处理config字段
        const configStr = JSON.stringify(values.config || {});
        
        const submitValues = {
            ...inputs,
            group: groupsString,
            models: modelsString,
            sort: parseInt(inputs.sort),
            weight: parseInt(inputs.weight),
            // overFrequencyAutoDisable: values.overFrequencyAutoDisable,
            retryInterval: parseInt(inputs.retryInterval),//自动重试格式化为int
            request_token_limit_enabled: values.request_token_limit_enabled,
            min_request_token_count: parseInt(values.min_request_token_count || 0),
            max_request_token_count: parseInt(values.max_request_token_count || 0),
            config: configStr,
        };
        if (inputs.sort > 10000 || inputs.weight > 10000) {
            AntdMessage.warning('排序和权重不能超过10000');
            return;
        }

        if (!coverAll) {
            allSettings.forEach(setting => {
                if (!coveredSettings.includes(setting)) {
                    submitValues[setting] = null;
                }
            });
        }

        setSubmitting(true);
        let isSuccess = false;
        try {
            let response: AxiosResponse<any, any>;
            const syncParam = inputs.sync_both_db ? '?syncBothDB=true' : '';

            if (isEdit) {
                response = await API.put(`/api/channelGroup/${syncParam}`, {...submitValues, id: parseInt(channelGroupId)});
            } else {
                response = await API.post(`/api/channelGroup/${syncParam}`, submitValues);
            }
            if (response.data.success) {
                isSuccess = true;
                AntdMessage.success(isEdit ? '渠道组信息更新成功!' : '渠道组创建成功!');
                form.resetFields();
                // 传递成功标志和编辑的渠道组ID给父组件
                closeModal(true, isEdit ? parseInt(channelGroupId) : null);
                return; // 成功后直接返回，不执行 finally 中的 closeModal
            } else {
                AntdMessage.error('操作失败: ' + response.data.message);
            }
        } catch (error) {
            AntdMessage.error('操作失败: ' + error.message);
        } finally {
            setSubmitting(false);
            // 只有在失败或取消时才调用不带参数的 closeModal
            if (!isSuccess) {
                closeModal();
            }
        }
    };

    return (
        <Spin spinning={loading}>
            <Row justify='center'>
                <Col span={24}>
                    <Form form={form} onFinish={onFinish} requiredMark={false}>
                        {/*名称*/}
                        <Form.Item label='名称' name='name' rules={[{required: true, message: '请输入渠道组名称!'}]}>
                            <Input
                                name='name'
                                value={inputs.name}
                                placeholder='请输入渠道组名称'
                                onChange={(e) => {
                                    setInputs((inputs) => ({...inputs, 'name': e.target.value}));
                                }}
                            />
                        </Form.Item>
                        {/*状态*/}
                        <Form.Item label='状态' name='status' rules={[{required: true, message: '请选择状态!'}]}>
                            <Select placeholder='请设置渠道组状态' value={inputs.status}>
                                <Select.Option value={1}>启用</Select.Option>
                                <Select.Option value={2}>禁用</Select.Option>
                            </Select>
                        </Form.Item>
                        {/*分组*/}
                        <Form.Item label='可用分组' name='group' rules={[{required: true, message: '请选择可用分组!'}]}>
                            <Select mode='multiple' placeholder='请选择可用分组' value={inputs.group}
                                    options={groupOptions}
                                    allowClear/>
                        </Form.Item>
                        {/*可用模型*/}
                        <Form.Item label='可用模型' name='models'
                                   rules={[{required: true, message: '请选择可用模型!'}]}>
                            <Select mode='multiple' placeholder='选择/搜索该渠道组可用模型' value={inputs.models}
                                    allowClear>
                                {modelOptions.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                        {option.text}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        {/*模型操作*/}
                        <Form.Item label='模型操作'>
                            <Flex gap={8}>
                                <Button onClick={() => {
                                    form.setFieldsValue({models: basicModels});
                                    AntdMessage.info('已添加 GPT-3.5 模型');
                                }}>GPT-3.5模型</Button>
                                <Button onClick={() => {
                                    form.setFieldsValue({models: advancedModels});
                                    AntdMessage.info('已添加 GPT-4 模型(不含32k)');
                                }}>GPT-4模型</Button>
                                <Button onClick={() => {
                                    form.setFieldsValue({models: allGptModels});
                                    AntdMessage.info('已添加全部 GPT 模型');
                                }}>GPT模型</Button>
                            </Flex>
                        </Form.Item>
                        {/*自定义模型*/}
                        <Form.Item label='自定义模型'>
                            <Space.Compact>
                                <Input
                                    placeholder='自定义模型名称'
                                    value={customModel}
                                    allowClear
                                    onChange={(e) => {
                                        setCustomModel(e.target.value);
                                    }}
                                    onPressEnter={() => {
                                        addCustomModel();
                                    }}
                                />
                                <Button onClick={addCustomModel}>添加</Button>
                            </Space.Compact>
                        </Form.Item>

                        {/*批量添加模型*/}
                        <Form.Item label='批量添加'>
                            <Space.Compact>
                                <Input
                                    placeholder='输入模型名称，用逗号分隔'
                                    value={inputModels}
                                    allowClear
                                    onChange={(e) => {
                                        setInputModels(e.target.value);
                                    }}
                                    onPressEnter={batchAddModels}
                                />
                                <Button onClick={batchAddModels}>批量添加</Button>
                            </Space.Compact>
                        </Form.Item>

                        {/*移除模型*/}
                        <Form.Item label='移除模型'>
                            <Space.Compact>
                                <Input
                                    placeholder='输入要移除的模型前缀'
                                    value={prefixToRemove}
                                    allowClear
                                    onChange={(e) => setPrefixToRemove(e.target.value)}
                                    onPressEnter={removeModelsByPrefix}
                                />
                                <Button onClick={removeModelsByPrefix}>移除前缀模型</Button>
                            </Space.Compact>
                        </Form.Item>

                        {/*保留模型*/}
                        <Form.Item label='保留模型'>
                            <Space.Compact>
                                <Input
                                    placeholder='输入要保留的模型前缀'
                                    value={prefixToKeep}
                                    allowClear
                                    onChange={(e) => setPrefixToKeep(e.target.value)}
                                    onPressEnter={keepModelsByPrefix}
                                />
                                <Button onClick={keepModelsByPrefix}>只保留前缀模型</Button>
                            </Space.Compact>
                        </Form.Item>

                        {/*代理*/}
                        <Form.Item label='代理地址' tooltip='此项可选，填写渠道使用的代理地址'
                                   rules={[{type: 'url', warningOnly: true}]}>
                            <Input
                                name='base_url'
                                value={inputs.base_url}
                                allowClear
                                status={(!isUrl(inputs.base_url) && inputs.base_url.length !== 0) ? 'warning' : ''}
                                onChange={(e) => {
                                    setInputs((inputs) => ({...inputs, 'base_url': e.target.value}));
                                }}
                                autoComplete='new-password'
                            />
                            {/*<Checkbox></Checkbox>*/}
                        </Form.Item>

                        {/*顺序*/}
                        <Form.Item label='强制顺序' tooltip='大的先使用，用完才会用小的'>
                            <Input
                                min={0}
                                status={inputs.weight < 0 ? 'error' : ''}
                                name='sort'
                                placeholder='此项可选，默认值0，大的先使用，用完才会用小的'
                                value={inputs.sort}
                                onChange={(e) => {
                                    let value = e.target.value;
                                    value = value ? parseInt(value, 10) : 0;
                                    setInputs((inputs) => ({...inputs, 'sort': value}));
                                }}
                                autoComplete='new-password'
                                type='number'
                            />
                        </Form.Item>
                        {/*权重*/}
                        <Form.Item label='渠道权重' tooltip='顺序相同，权重大的使用概率大'>
                            <Input
                                min={0}
                                status={inputs.weight < 0 ? 'error' : ''}
                                name='weight'
                                placeholder='此项可选，默认值0，值越大，使用概率越大'
                                value={inputs.weight}
                                onChange={(e) => {
                                    let value = e.target.value;
                                    value = value ? parseInt(value, 10) : 0;
                                    setInputs((inputs) => ({...inputs, 'weight': value}));
                                }}
                                autoComplete='new-password'
                                type='number'
                            />
                        </Form.Item>

                        <Divider orientation="right">Token 限制</Divider>
                        <Form.Item
                            name="request_token_limit_enabled"
                            label="请求Token限制"
                            tooltip="开启后将限制渠道请求Token数，如果超过范围则会重试其他渠道，也就是说会浪费掉一次重试的次数，所以请合理安排渠道优先级"
                            valuePropName="checked"
                        >
                            <Checkbox
                                onChange={(e) => {
                                    setInputs((inputs) => ({
                                        ...inputs,
                                        'request_token_limit_enabled': e.target.checked
                                    }));
                                }}
                            />
                        </Form.Item>
                        
                        {requestTokenLimitEnabled && (
                            <Form.Item
                                label="请求Token数范围"
                                required={false}
                            >
                                <Space>
                                    <Form.Item
                                        name="min_request_token_count"
                                        noStyle
                                    >
                                        <InputNumber 
                                            min={0} 
                                            placeholder="最小值"
                                            onChange={(value) => {
                                                setInputs((inputs) => ({
                                                    ...inputs,
                                                    'min_request_token_count': value
                                                }));
                                            }}
                                        />
                                    </Form.Item>
                                    <span>-</span>
                                    <Form.Item
                                        name="max_request_token_count"
                                        noStyle
                                    >
                                        <InputNumber 
                                            min={0} 
                                            placeholder="最大值"
                                            onChange={(value) => {
                                                setInputs((inputs) => ({
                                                    ...inputs,
                                                    'max_request_token_count': value
                                                }));
                                            }}
                                        />
                                    </Form.Item>
                                </Space>
                            </Form.Item>
                        )}

                        {/*重试周期*/}
                        <Form.Item label='重试周期'
                                   tooltip='自动禁用通道后的重试周期，单位秒（最大重试失败次数与设定的失败重试次数有关）'>
                            <AutoComplete
                                name='retryInterval'
                                value={inputs.retryInterval}
                                options={[
                                    {value: '60', label: '1分钟'},
                                    {value: '600', label: '10分钟'},
                                    {value: '1800', label: '30分钟'},
                                    {value: '-1', label: '不死模式'}
                                ]}
                                filterOption={(inputValue, option) =>
                                    option.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                                }
                                onChange={(v) => {
                                    setInputs((inputs) => ({
                                        ...inputs,
                                        'retryInterval': v ? String(parseInt(v, 10)) : ''
                                    }));
                                }}
                            />
                        </Form.Item>
                        {/*超频熔断*/}
                        <Form.Item>
                            <Checkbox
                                name='overFrequencyAutoDisable'
                                checked={inputs.overFrequencyAutoDisable}
                                onChange={(e) => {
                                    setInputs((inputs) => ({
                                        ...inputs,
                                        'overFrequencyAutoDisable': !inputs.overFrequencyAutoDisable
                                    }));
                                }}
                            /> 超频熔断（返回超过频率限制类错误时自动禁用渠道）
                        </Form.Item>
                        {/*选择覆盖项目*/}
                        {isEdit &&
                            <Form.Item>
                                <Checkbox value={coverAll} checked={coverAll} onChange={() => setCoverAll(!coverAll)}>
                                    覆盖全部设置项
                                </Checkbox>
                                {!coverAll && (
                                    <Transfer
                                        style={{marginTop: 15}}
                                        oneWay
                                        operations={['需要覆盖', '不覆盖']}
                                        dataSource={allSettings.map(item => ({
                                            key: item,
                                            title: settingsMapping[item] || item // 使用映射转换为中文，如果没有映射则使用原英文名称
                                        }))}
                                        titles={['不覆盖', '覆盖']}
                                        targetKeys={coveredSettings}
                                        onChange={handleChange}
                                        render={item => item.title}
                                    />
                                )}
                            </Form.Item>
                        }

                        <Form.Item>
                            <Checkbox
                                name='sync_both_db'
                                checked={inputs.sync_both_db}
                                onChange={(e) => {
                                    setInputs((inputs) => ({
                                        ...inputs,
                                        'sync_both_db': e.target.checked
                                    }));
                                }}
                            /> 双数据库同步（开启后操作将同时更新SQL和NoSQL数据库，用于数据迁移和备份验证）
                        </Form.Item>

                        <Divider orientation="right">渠道代理设置</Divider>
                        <Form.Item
                            name={["config", "proxy_enabled"]}
                            label="启用专属代理"
                            tooltip="开启后，该渠道组的所有请求都将使用配置的代理，会覆盖渠道自身的代理设置"
                            valuePropName="checked"
                        >
                            <Checkbox
                                onChange={(e) => {
                                    setInputs((inputs) => {
                                        const configObj = inputs.config ? JSON.parse(inputs.config) : {};
                                        configObj.proxy_enabled = e.target.checked;
                                        return {
                                            ...inputs,
                                            config: JSON.stringify(configObj)
                                        };
                                    });
                                }}
                            />
                        </Form.Item>

                        {proxyEnabled && (
                            <>
                                <Form.Item
                                    name={["config", "proxy_url"]}
                                    label="代理服务器URL"
                                    tooltip="代理服务器地址，格式：http://host:port 或 socks5://host:port"
                                >
                                    <Input 
                                        placeholder="例如：http://127.0.0.1:7890 或 socks5://127.0.0.1:1080"
                                        onChange={(e) => {
                                            setInputs((inputs) => {
                                                const configObj = inputs.config ? JSON.parse(inputs.config) : {};
                                                configObj.proxy_url = e.target.value;
                                                return {
                                                    ...inputs,
                                                    config: JSON.stringify(configObj)
                                                };
                                            });
                                        }}
                                    />
                                </Form.Item>

                                <Form.Item
                                    name={["config", "proxy_auth"]}
                                    label="代理认证信息"
                                    tooltip="代理服务器认证信息，格式：Basic base64(username:password)"
                                >
                                    <Input 
                                        placeholder="例如：Basic dXNlcm5hbWU6cGFzc3dvcmQ="
                                        onChange={(e) => {
                                            setInputs((inputs) => {
                                                const configObj = inputs.config ? JSON.parse(inputs.config) : {};
                                                configObj.proxy_auth = e.target.value;
                                                return {
                                                    ...inputs,
                                                    config: JSON.stringify(configObj)
                                                };
                                            });
                                        }}
                                    />
                                </Form.Item>
                            </>
                        )}

                        <Form.Item>
                            <Space size='middle' style={{float: 'right'}}>
                                <Button onClick={() => closeModal()}>取消</Button>
                                <Tooltip title={isEdit ? '将影响所有绑定的渠道' : ''}>
                                    <Button type='primary' htmlType='submit'
                                            loading={submitting}>{isEdit ? '更新' : '创建'}</Button>
                                </Tooltip>
                            </Space>
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
        </Spin>
    );
};

export default AddChannelGroupPage;
