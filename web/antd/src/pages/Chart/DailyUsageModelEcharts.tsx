import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { API, isAdmin, isMobile } from "../../helpers";
import { UserContext } from "../../context/User";
import { Empty, Spin, Select, Space, Typography, Button } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import moment from 'moment';
import { StatusContext } from "../../context/Status";

const { Option } = Select;
const { Title } = Typography;

interface DailyUsageByModelEchartsProps {
  searchParams: URLSearchParams;
  startTimestamp?: number;
  endTimestamp?: number;
  tokenName?: string;
  username?: string;
  modelName?: string;
  channel?: number;
  channelName?: string;
  dimension?: string;
  granularityParam?:string;
  onModelsChange?: (models: string[]) => void; // 新增：模型列表变化回调
}

const DailyUsageByModelEcharts: React.FC<DailyUsageByModelEchartsProps> = ({
  searchParams,
  startTimestamp,
  endTimestamp,
  tokenName,
  username,
  modelName,
  channel,
  channelName,
  dimension = 'model',
  granularityParam = 'day',
  onModelsChange
}) => {
    const { t } = useTranslation();
    const [userState] = useContext(UserContext);
    const [statusState] = useContext(StatusContext);
    const [dailyUserUsageStats, setDailyUserUsageStats] = useState([]);
    const [loadingDailyUsage, setLoadingDailyUsage] = useState(true);
    const [dailyUsageData, setDailyUsageData] = useState([]);
    const [granularity, setGranularity] = useState(granularityParam === 'hour' ? 'hour' : granularityParam === 'week' ? 'week' : granularityParam === 'month' ? 'month' : 'day');
    const [viewMode, setViewMode] = useState<'usage' | 'cost'>('usage');

    useEffect(() => {
        const requestKey = JSON.stringify({
            granularity,
            start_timestamp: startTimestamp,
            end_timestamp: endTimestamp,
            token_name: tokenName,
            username,
            model_name: modelName,
            channel,
            channel_name: channelName,
            dimension: viewMode === 'cost' ? `cost_by_${dimension}` : dimension
        });

        const controller = new AbortController();

        const fetchData = async () => {
            if (!requestKey) return;

            setLoadingDailyUsage(true);
            try {
                await fetchDailyUsageData({
                    granularity,
                    start_timestamp: startTimestamp,
                    end_timestamp: endTimestamp,
                    token_name: tokenName,
                    username,
                    model_name: modelName,
                    channel,
                    channel_name: channelName,
                    dimension: viewMode === 'cost' ? `cost_by_${dimension}` : dimension
                }, controller.signal);
            } finally {
                setLoadingDailyUsage(false);
            }
        };

        const timeoutId = setTimeout(fetchData, 300);

        return () => {
            clearTimeout(timeoutId);
            controller.abort();
        };
    }, [
        granularity,
        startTimestamp,
        endTimestamp,
        tokenName,
        username,
        modelName,
        channel,
        channelName,
        dimension,
        viewMode
    ]);

    useEffect(() => {
        if (granularity === 'hour') {
            setGranularity('hour');
        } else if (granularity === 'week') {
            setGranularity('week');
        } else if (granularity === 'month') {
            setGranularity('month');
        } else if (granularity === 'day') {
            setGranularity('day');
        }
    }, [granularity]);

    const fetchDailyUsageData = async (params, signal?: AbortSignal) => {
        try {
            const useNewDataApi = statusState.status.DataExportEnabled && 
                                 statusState.status.DataExportDisplayEnabled;
            
            let url;
            if (useNewDataApi) {
                url = isAdmin(userState)
                    ? '/api/data'
                    : '/api/data/self';
            } else {
                url = isAdmin(userState)
                    ? '/api/log/getDailyUsageStatsByDimension'
                    : '/api/log/self/getDailyUsageStatsByDimension';
            }

            const requestParams = {
                ...params,
                granularity: params.granularity || 'day'
            };

            const res = await API.get(url, { 
                params: requestParams,
                signal 
            });
            
            const { success, data } = res.data;
            if (success) {
                setDailyUserUsageStats(data);
                setDailyUsageData(data);
            }
        } catch (error: any) {
            if (error.name === 'AbortError' || error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
                return;
            }
            console.error(t('dailyUsageChart.fetchError'), error);
        }
    };

    const formatData = (data: any[]) => {
        if (!data || data.length === 0) {
            return { dates: [], models: [], formattedData: {} };
        }
        const formattedData: { [key: string]: { [key: string]: number } } = {};
        const models: string[] = [];
        const modelUsage: { [key: string]: number } = {};

        data.forEach(item => {
            const { modelName, date } = item;
            const value = viewMode === 'cost' ? item.costUsd : item.sumUsd;
            
            if (!formattedData[date]) {
                formattedData[date] = {};
            }
            if (!formattedData[date][modelName]) {
                formattedData[date][modelName] = 0;
            }
            formattedData[date][modelName] += value || 0;

            if (!models.includes(modelName)) {
                models.push(modelName);
                modelUsage[modelName] = 0;
            }
            modelUsage[modelName] += value || 0;
        });

        const sortedModels = models.sort((a, b) => modelUsage[b] - modelUsage[a]);
        
        const dates = Object.keys(formattedData).sort((a, b) => {
            if (granularity === 'week') {
                const [yearA, weekA] = a.split('-').map(Number);
                const [yearB, weekB] = b.split('-').map(Number);
                return yearA !== yearB ? yearA - yearB : weekA - weekB;
            }
            return a.localeCompare(b);
        });

        return { formattedData, models: sortedModels, dates };
    };

    const {formattedData, models, dates} = formatData(dailyUsageData);

    // 当模型列表变化时，通知父组件（只在合适的条件下更新，避免性能问题）
    useEffect(() => {
        if (onModelsChange && models.length > 0) {
            // 计算时间范围，只有在时间范围不太大时才更新模型列表
            const timeDiff = endTimestamp && startTimestamp ? endTimestamp - startTimestamp : 0;
            const daysDiff = timeDiff / (24 * 60 * 60); // 转换为天数

            // 只有在30天以内的数据才提供动态模型列表，避免数据量过大
            if (daysDiff <= 30) {
                // 只取前15个最常用的模型，避免列表过长
                const topModels = models.slice(0, 15);
                onModelsChange(topModels);
            }
        }
    }, [models, onModelsChange, startTimestamp, endTimestamp]);

    const options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: (params: any) => {
                const sortedParams = params.slice().sort((a: any, b: any) => b.value - a.value);
                let tooltip = `<b>${params[0].name}</b><br/>`;
                
                const total = params.reduce((sum: number, param: any) => sum + (param.value || 0), 0);
                
        
                tooltip += `<div style="margin: 5px 0; padding-bottom: 5px; border-bottom: 1px solid #ccc;">
                    <b>${t('dailyUsageByModel.tooltipTotal', { value: total.toFixed(4) })}</b>
                </div>`;

                let maxModelNameLength = 0;
                sortedParams.forEach((param: any) => {
                    maxModelNameLength = Math.max(maxModelNameLength, param.seriesName.length);
                });

                const modelNameWidth = isMobile() ? 80 : Math.max(120, maxModelNameLength * 8);

                sortedParams.forEach((param: any) => {
                    if (param.value !== 0) {
                        const value = typeof param.value === 'number' ? param.value.toFixed(4) : param.value;
                        const modelName = isMobile() && param.seriesName.length > 10 
                            ? `${param.seriesName.substring(0, 10)}...`
                            : param.seriesName;
                        
                        tooltip += `<div style="display:flex;justify-content:space-between;align-items:center;margin:2px 0;">
                            <span style="display:inline-block;margin-right:4px;border-radius:10px;width:8px;height:8px;background-color:${param.color};"></span>
                            <span style="display:inline-block;width:${modelNameWidth}px;text-align:left;font-size:${isMobile() ? '12px' : '14px'}">${modelName}:</span>
                            <span style="display:inline-block;width:70px;text-align:right;font-size:${isMobile() ? '12px' : '14px'}">$ ${value}</span>
                        </div>`;
                    }
                });
                return tooltip;
            },
            position: function (point: number[], params: any, dom: HTMLElement, rect: DOMRect, size: { contentSize: number[]; viewSize: number[] }) {
                const [x, y] = point;
                const { contentSize, viewSize } = size;
                const [width, height] = contentSize;
                const [viewWidth, viewHeight] = viewSize;
                
                if (isMobile()) {
                    const padding = 20;
                    return [
                        Math.max(padding, Math.min(x - width / 2, viewWidth - width - padding)),
                        padding
                    ];
                }
                
                return [x + 15, y];
            },
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#ccc',
            borderWidth: 1,
            padding: isMobile() ? 5 : 8,
            textStyle: {
                color: '#333',
                fontSize: isMobile() ? 12 : 14
            },
            extraCssText: isMobile() 
                ? 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3); max-width: 90vw;' 
                : 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);'
        },
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 0,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
                fontSize: 10,
                width: 200,
                overflow: 'break',
                lineHeight: 12,
            },
            pageButtonPosition: 'end',
            pageTextStyle: {
                color: '#888',
            },
            height: '15%',
        },
        grid: {
            left: '3%',
            right: '3%',
            bottom: '15%',
            top: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: dates,
            axisLabel: {
                formatter: (value: string) => {
                    switch (granularity) {
                        case 'hour':
                            return moment(value, 'YYYY-MM-DD HH:mm').format('MM-DD HH:00');
                        case 'day':
                            return moment(value, 'YYYY-MM-DD').format('MM-DD');
                        case 'week':
                            const [year, week] = value.split('-');
                            return `${year} W${week}`;
                        case 'month':
                            return moment(value, 'YYYY-MM').format('YYYY-MM');
                        default:
                            return value;
                    }
                }
            }
        },
        yAxis: {
            name: t('dailyUsageChart.yAxisName')
        },
        series: models.map(model => ({
            name: model,
            type: 'bar',
            stack: 'total',
            data: dates.map(date => formattedData[date][model] || 0),
            barMaxWidth: 50
        }))
    };

    const getChartTitle = () => {
        switch (granularity) {
            case 'hour':
                return t('modelUsageChart.hourlyTitle');
            case 'day':
                return t('modelUsageChart.dailyTitle');
            case 'week':
                return t('modelUsageChart.weeklyTitle');
            case 'month':
                return t('modelUsageChart.monthlyTitle');
            default:
                return t('modelUsageChart.title');
        }
    };

    return (
        <div>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Space>
                    <Title level={4}>{getChartTitle()}</Title>
                    <Select 
                        value={granularity} 
                        onChange={setGranularity}
                        style={{ width: 150 }}
                        dropdownMatchSelectWidth={false}
                    >
                        <Option value="hour">{t('granularity.hour')}</Option>
                        <Option value="day">{t('granularity.day')}</Option>
                        <Option value="week">{t('granularity.week')}</Option>
                        <Option value="month">{t('granularity.month')}</Option>
                    </Select>
                    {isAdmin(userState) && (
                        <Button onClick={() => setViewMode(prev => prev === 'usage' ? 'cost' : 'usage')}>
                            {t('viewMode.switchTo', {
                                mode: t(viewMode === 'usage' ? 'viewMode.cost' : 'viewMode.usage')
                            })}
                        </Button>
                    )}
                </Space>
                <Spin tip={t('dailyUsageChart.loadingTip')} indicator={<LoadingOutlined style={{fontSize: 24}} spin/>}
                      spinning={loadingDailyUsage}>
                    {dailyUserUsageStats && dailyUserUsageStats.length > 0 ?
                        <ReactECharts option={options} style={{ height: '240px' }} /> :
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{height: 240}}/>
                    }
                </Spin>
            </Space>
        </div>
    );
};

export default DailyUsageByModelEcharts;
